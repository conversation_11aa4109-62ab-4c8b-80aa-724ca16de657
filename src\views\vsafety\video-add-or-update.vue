<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
          <el-form-item label="视频上传" prop="videoFile">
        <el-upload
          class="upload-demo"
          :auto-upload="false"
          :show-file-list="true"
          :on-change="handleFileChange"
          accept="video/*"
          :limit="1"
        >
          <el-button type="primary">选择视频文件</el-button>
          <template #tip>
            <div class="el-upload__tip">
              只能上传视频文件，且不超过500MB
            </div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="存储路径(可选)" prop="storagePath">
        <el-input v-model="dataForm.storagePath" placeholder="MinIO存储路径，不填则使用默认路径"></el-input>
      </el-form-item>
      <el-form-item label="视频链接" prop="vidUrl">
        <el-input v-model="dataForm.vidUrl" placeholder="视频链接（上传后自动填充）" :disabled="true"></el-input>
      </el-form-item>
          <el-form-item label="平台信息" prop="platformId">
        <el-input v-model="dataForm.platformId" placeholder="外键，关联平台表"></el-input>
      </el-form-item>
          <el-form-item label="标注类型" prop="labelId">
        <el-input v-model="dataForm.labelId" placeholder="标注类型"></el-input>
      </el-form-item>
          <el-form-item label="风险类型" prop="riskId">
        <el-input v-model="dataForm.riskId" placeholder="风险类型"></el-input>
      </el-form-item>
          <el-form-item label="对应事件id" prop="detailId">
        <el-input v-model="dataForm.detailId" placeholder="对应事件id"></el-input>
      </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { uploadFile } from "@/utils/api";
import type { UploadFile } from "element-plus";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const selectedFile = ref<File | null>(null);

const dataForm = reactive({
  id: '',  
  vidUrl: '',  
  platformId: '',  
  labelId: '',  
  riskId: '',  
  detailId: '',
  storagePath: ''
});

const rules = ref({
          vidUrl: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
    //       platformId: [
    //   { required: true, message: '必填项不能为空', trigger: 'blur' }
    // ],
    //       labelId: [
    //   { required: true, message: '必填项不能为空', trigger: 'blur' }
    // ],
    //       riskId: [
    //   { required: true, message: '必填项不能为空', trigger: 'blur' }
    // ],
    //       detailId: [
    //   { required: true, message: '必填项不能为空', trigger: 'blur' }
    // ]
  });

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";
  selectedFile.value = null; // 重置文件选择
  dataForm.storagePath = ""; // 重置存储路径

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/vsafety/video/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 文件选择处理
const handleFileChange = (file: UploadFile) => {
  if (file.raw) {
    // 检查文件大小（500MB = 500 * 1024 * 1024 bytes）
    if (file.raw.size > 500 * 1024 * 1024) {
      ElMessage.error('文件大小不能超过500MB');
      return;
    }
    selectedFile.value = file.raw;
  }
};

// 上传视频文件
const uploadVideoFile = async (): Promise<string> => {
  if (!selectedFile.value) {
    throw new Error('请先选择视频文件');
  }
  
  try {
    const res = await uploadFile(selectedFile.value, dataForm.storagePath || undefined);
    return res.data.url || res.data.path; // 根据后端返回的字段调整
  } catch (error) {
    console.error('视频上传失败:', error);
    throw new Error('视频上传失败');
  }
};

// 表单提交
const dataFormSubmitHandle = async () => {
  dataFormRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      return false;
    }

    try {
        console.log("表单提交", dataForm.vidUrl, selectedFile.value);
        // 如果有选择文件且没有视频链接，先上传文件
      if (selectedFile.value && !dataForm.vidUrl) {
        ElMessage.info('正在上传视频文件...');
        const uploadedUrl = await uploadVideoFile();
        dataForm.vidUrl = uploadedUrl;
        ElMessage.success('视频上传成功');
      }

      // 提交表单数据
      (!dataForm.id ? baseService.post : baseService.put)("/vsafety/video", dataForm).then((res) => {
        ElMessage.success({
          message: '操作成功',
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          }
        });
      });
    } catch (error) {
      ElMessage.error(error instanceof Error ? error.message : '操作失败');
    }
  });
};

defineExpose({
  init
});
</script>
