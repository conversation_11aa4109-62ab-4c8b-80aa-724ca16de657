<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="140px">
          <el-form-item label="外键，关联平台表" prop="platformId">
        <el-input v-model="dataForm.platformId" placeholder="外键，关联平台表"></el-input>
      </el-form-item>
          <el-form-item label="外键，关联标注表" prop="labelId">
        <el-input v-model="dataForm.labelId" placeholder="外键，关联标注表"></el-input>
      </el-form-item>
          <el-form-item label="外键，关联风险表" prop="riskId">
        <el-input v-model="dataForm.riskId" placeholder="外键，关联风险表"></el-input>
      </el-form-item>
          <el-form-item label="主体内容" prop="context">
        <el-input v-model="dataForm.context" placeholder="主体内容"></el-input>
      </el-form-item>
          <el-form-item label="视频id" prop="videoId">
        <el-input v-model="dataForm.videoId" placeholder="视频id"></el-input>
      </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '',  platformId: '',  labelId: '',  riskId: '',  context: '',  videoId: ''});

const rules = ref({
          platformId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          labelId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          riskId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          context: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          videoId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ]
  });

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/vsafety/videodetail/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/vsafety/videodetail", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
