<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
          <el-form-item label="图片链接" prop="picUrl">
        <el-input v-model="dataForm.picUrl" placeholder="图片链接"></el-input>
      </el-form-item>
          <el-form-item label="外键，对应事件id" prop="detailId">
        <el-input v-model="dataForm.detailId" placeholder="外键，对应事件id"></el-input>
      </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '',  picUrl: '',  detailId: ''});

const rules = ref({
          picUrl: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          detailId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ]
  });

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/vsafety/picture/" + id).then((res) => {
    Object.assign(dataForm, res.data);
    // 删除分析结果，确保新增时分析结果为空
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    // 创建提交数据的副本，确保不包含分析结果
    const submitData = { ...dataForm };
    // 删除可能存在的分析结果字段
    delete submitData.analysisResult;
    
    (!dataForm.id ? baseService.post : baseService.put)("/vsafety/picture", submitData).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
