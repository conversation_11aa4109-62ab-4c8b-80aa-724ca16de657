<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
          <el-form-item label="详情名" prop="name">
        <el-input v-model="dataForm.name" placeholder="详情名"></el-input>
      </el-form-item>
          <el-form-item label="来源" prop="source">
        <el-input v-model="dataForm.source" placeholder="来源"></el-input>
      </el-form-item>
          <el-form-item label="作者" prop="author">
        <el-input v-model="dataForm.author" placeholder="作者"></el-input>
      </el-form-item>
          <el-form-item label="时间" prop="time">
        <el-input v-model="dataForm.time" placeholder="时间"></el-input>
      </el-form-item>
          <el-form-item label="事件id" prop="evenId">
        <el-input v-model="dataForm.evenId" placeholder="事件id"></el-input>
      </el-form-item>
          <el-form-item label="内容" prop="content">
        <el-input v-model="dataForm.content" placeholder="内容"></el-input>
      </el-form-item>
          <el-form-item label="性质id" prop="natureId">
        <el-input v-model="dataForm.natureId" placeholder="性质id"></el-input>
      </el-form-item>
          <el-form-item label="行业id" prop="industryId">
        <el-input v-model="dataForm.industryId" placeholder="行业id"></el-input>
      </el-form-item>
          <el-form-item label="url" prop="url">
        <el-input v-model="dataForm.url" placeholder="url"></el-input>
      </el-form-item>
          <el-form-item label="省份id" prop="provinceId">
        <el-input v-model="dataForm.provinceId" placeholder="省份id"></el-input>
      </el-form-item>
          <el-form-item label="城市id" prop="cityId">
        <el-input v-model="dataForm.cityId" placeholder="城市id"></el-input>
      </el-form-item>
          <el-form-item label="图片url" prop="imgUrl">
        <el-input v-model="dataForm.imgUrl" placeholder="图片url"></el-input>
      </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '',  name: '',  source: '',  author: '',  time: '',  evenId: '',  content: '',  natureId: '',  industryId: '',  url: '',  provinceId: '',  cityId: '',  imgUrl: ''});

const rules = ref({
          name: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          source: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          author: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          time: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          evenId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          content: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          natureId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          industryId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          url: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          provinceId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          cityId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          imgUrl: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ]
  });

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/vsafety/envdetail/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/vsafety/envdetail", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
