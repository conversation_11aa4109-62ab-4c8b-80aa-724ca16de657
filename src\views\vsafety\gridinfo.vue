<template>
  <div class="mod-vsafety__gridinfo">
    <!-- 顶部操作栏 -->
    <div class="action-bar">
      <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
        <el-form-item>
          <el-button type="primary" @click="addOrUpdateHandle()" :icon="Plus">
            新增事件
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button 
            type="danger" 
            @click="state.deleteHandle()" 
            :icon="Delete"
            :disabled="!state.dataListSelections.length"
          >
            批量删除
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 卡片式展示 -->
    <div class="card-container" v-loading="state.dataListLoading">
      <div 
        class="info-card" 
        v-for="item in state.dataList" 
        :key="item.id"
        :class="{ 'selected': state.dataListSelections.some(sel => sel.id === item.id) }"
      >
        <div class="card-header">
          <el-checkbox 
            v-model="item.checked" 
            @change="toggleSelection(item)"
          />
          <span class="serial-number">{{ item.serialNumber }}</span>
          <el-tag :type="getLevelTagType(item.level)" size="small">
            {{ item.level }}
          </el-tag>
          <el-tag :type="getStatusTagType(item.issueStatus)" size="small">
            {{ getStatusText(item.issueStatus) }}
          </el-tag>
        </div>
        
        <div class="card-content">
          <div class="info-item">
            <span class="label">事件名称:</span>
            <span class="value">{{ item.subject }}</span>
          </div>
          <div class="info-item">
            <span class="label">发生地点:</span>
            <span class="value">{{ item.occurLocation }}</span>
          </div>
          <div class="info-item">
            <span class="label">发生时间:</span>
            <span class="value">{{ item.occurTime }}</span>
          </div>
          <div class="info-item">
            <span class="label">当前部门:</span>
            <span class="value">{{ item.currentDeptName }}</span>
          </div>
        </div>
        
        <div class="card-footer">
          <el-button 
            type="primary" 
            size="small" 
            @click="showDetail(item)"
            :icon="View"
          >
            详情
          </el-button>
          <el-button 
            type="primary" 
            size="small" 
            @click="addOrUpdateHandle(item.id)"
            :icon="Edit"
          >
            修改
          </el-button>
          <el-button 
            type="danger" 
            size="small" 
            @click="state.deleteHandle(item.id)"
            :icon="Delete"
          >
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <el-pagination 
      :current-page="state.page" 
      :page-sizes="[10, 20, 50, 100]" 
      :page-size="state.limit" 
      :total="state.total" 
      layout="total, sizes, prev, pager, next, jumper" 
      @size-change="state.pageSizeChangeHandle" 
      @current-change="state.pageCurrentChangeHandle"
    ></el-pagination>
    
    <!-- 详情弹窗 -->
    <el-dialog 
  v-model="detailVisible" 
  :title="currentDetail?.subject || '事件详情'" 
  width="70%"
  class="detail-dialog"
  :before-close="handleClose"
>
  <div class="detail-container">
    <el-scrollbar max-height="60vh">
      <!-- 基本信息分组 -->
      <el-card class="detail-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><InfoFilled /></el-icon>
            <span>基本信息</span>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="事件单号">{{ currentDetail?.serialNumber }}</el-descriptions-item>
          <el-descriptions-item label="事件名称">{{ currentDetail?.subject }}</el-descriptions-item>
          <el-descriptions-item label="事件来源">{{ currentDetail?.source }}</el-descriptions-item>
          <el-descriptions-item label="事件类型编码">{{ currentDetail?.categoryCode }}</el-descriptions-item>
          <el-descriptions-item label="事件类型">{{ currentDetail?.categoryName }}</el-descriptions-item>
          <el-descriptions-item label="事件等级">
            <el-tag :type="getLevelTagType(currentDetail?.level)" size="small">
              {{ currentDetail?.level }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="事件状态">
            <el-tag :type="getStatusTagType(currentDetail?.issueStatus)" size="small">
              {{ getStatusText(currentDetail?.issueStatus) }}
            </el-tag>
            <span class="status-hint">（0-不予受理 -1-待受理 1-办理中 2-待核查 3-已结案）</span>
          </el-descriptions-item>
          <el-descriptions-item label="风险值">{{ currentDetail?.riskValue }}</el-descriptions-item>
          <el-descriptions-item label="类别">{{ currentDetail?.category }}</el-descriptions-item>
          <el-descriptions-item label="事件链">{{ currentDetail?.genre }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 时间信息分组 -->
      <el-card class="detail-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Clock /></el-icon>
            <span>时间信息</span>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="发生时间">{{ currentDetail?.occurTime }}</el-descriptions-item>
          <el-descriptions-item label="结案时间">{{ currentDetail?.doneTime }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentDetail?.createTime }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ currentDetail?.updateTime }}</el-descriptions-item>
          <el-descriptions-item label="最近办理时间">{{ currentDetail?.lastProcessTime }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 地点信息分组 -->
      <el-card class="detail-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Location /></el-icon>
            <span>地点信息</span>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="发生地点">{{ currentDetail?.occurLocation }}</el-descriptions-item>
          <el-descriptions-item label="经纬度">{{ currentDetail?.lng }}, {{ currentDetail?.lat }}</el-descriptions-item>
          <el-descriptions-item label="坐标系类型">{{ currentDetail?.coordinateType }}</el-descriptions-item>
          <el-descriptions-item label="发生网络ID">{{ currentDetail?.occurGrid }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 部门信息分组 -->
      <el-card class="detail-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><OfficeBuilding /></el-icon>
            <span>部门信息</span>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="创建部门">{{ currentDetail?.createDept }}</el-descriptions-item>
          <el-descriptions-item label="创建部门名称">{{ currentDetail?.createDeptName }}</el-descriptions-item>
          <el-descriptions-item label="所属网络">{{ currentDetail?.deptId }}</el-descriptions-item>
          <el-descriptions-item label="当前部门组织编码">{{ currentDetail?.currentDeptCode }}</el-descriptions-item>
          <el-descriptions-item label="当前办理部门名称">{{ currentDetail?.currentDeptName }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 人员信息分组 -->
      <el-card class="detail-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><User /></el-icon>
            <span>人员信息</span>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="创建人">{{ currentDetail?.createBy }}</el-descriptions-item>
          <el-descriptions-item label="更新人">{{ currentDetail?.updateBy }}</el-descriptions-item>
          <el-descriptions-item label="录入人员名称">{{ currentDetail?.enterName }}</el-descriptions-item>
          <el-descriptions-item label="催办人ID">{{ currentDetail?.uringPersonId }}</el-descriptions-item>
          <el-descriptions-item label="黄牌督办人ID">{{ currentDetail?.yellowSupervise }}</el-descriptions-item>
          <el-descriptions-item label="红牌督办人ID">{{ currentDetail?.redSupervise }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 状态信息分组 -->
      <el-card class="detail-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Flag /></el-icon>
            <span>状态信息</span>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="是否重大">
            <el-tag :type="currentDetail?.isImportant === '1' ? 'danger' : 'info'" size="small">
              {{ currentDetail?.isImportant === '1' ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否紧急">
            <el-tag :type="currentDetail?.isUrgent === '1' ? 'danger' : 'info'" size="small">
              {{ currentDetail?.isUrgent === '1' ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否锁定">
            <el-tag :type="currentDetail?.isLocked === '1' ? 'warning' : 'info'" size="small">
              {{ currentDetail?.isLocked === '1' ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否限时办结">
            <el-tag :type="currentDetail?.hasDeadline === '1' ? 'warning' : 'info'" size="small">
              {{ currentDetail?.hasDeadline === '1' ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否超时">
            <el-tag :type="currentDetail?.isTimeout === '1' ? 'danger' : 'success'" size="small">
              {{ currentDetail?.isTimeout === '1' ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="删除状态">
            <el-tag :type="currentDetail?.delFlag === '1' ? 'danger' : 'success'" size="small">
              {{ currentDetail?.delFlag === '1' ? '已删除' : '正常' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否作废申请中">
            <el-tag :type="currentDetail?.isBeingInvalidated === '1' ? 'warning' : 'info'" size="small">
              {{ currentDetail?.isBeingInvalidated === '1' ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否领导批示事件">
            <el-tag :type="currentDetail?.isInstructions === '1' ? 'warning' : 'info'" size="small">
              {{ currentDetail?.isInstructions === '1' ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否有民转刑，刑转命风险">{{ currentDetail?.isThereRiskCivilToCriminal }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 附件与协同信息分组 -->
      <el-card class="detail-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Document /></el-icon>
            <span>附件与协同信息</span>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="附件">{{ currentDetail?.attachment }}</el-descriptions-item>
          <el-descriptions-item label="有无附件">{{ currentDetail?.hasAttachment }}</el-descriptions-item>
          <el-descriptions-item label="协同单号">{{ currentDetail?.collaborationNo }}</el-descriptions-item>
          <el-descriptions-item label="协同来源">{{ currentDetail?.collaborationSource }}</el-descriptions-item>
          <el-descriptions-item label="黄牌督办次数">{{ currentDetail?.yellowSuperviseCount }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 关联信息分组 -->
      <el-card class="detail-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Connection /></el-icon>
            <span>关联信息</span>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="前序ID">{{ currentDetail?.precedeId }}</el-descriptions-item>
          <el-descriptions-item label="后续ID">{{ currentDetail?.subsequentId }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 文本信息分组 -->
      <el-card class="detail-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Memo /></el-icon>
            <span>文本信息</span>
          </div>
        </template>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="事件描述">
            <div class="text-content">{{ currentDetail?.issueDescription || '-' }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="涉及人员">
            <div class="text-content">{{ currentDetail?.personnelInvolved || '-' }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="质量评析">
            <div class="text-content">{{ currentDetail?.qualityAnalysis || '-' }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </el-scrollbar>
  </div>
  
  <template #footer>
    <el-button @click="handleClose">关闭</el-button>
  </template>
</el-dialog>
    
    <!-- 新增/修改弹窗 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./gridinfo-add-or-update.vue";
import { Plus, Delete, Edit, Search, View } from '@element-plus/icons-vue';
import { InfoFilled, Clock, Location, OfficeBuilding, User, Flag, Document, Connection, Memo } from '@element-plus/icons-vue';

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/vsafety/gridinfo/page",
  getDataListIsPage: true,
  exportURL: "/vsafety/gridinfo/export",
  deleteURL: "/vsafety/gridinfo",
  dataForm: {
    keyword: ''
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

// 详情弹窗控制
const detailVisible = ref(false);
const currentDetail = ref<any>(null);

const showDetail = (item: any) => {
  currentDetail.value = item;
  detailVisible.value = true;
};

// 关闭弹窗
const handleClose = () => {
  detailVisible.value = false;
  currentDetail.value = null;
};

// 获取事件等级标签类型
const getLevelTagType = (level: string) => {
  switch(level) {
    case '一级': return 'danger';
    case '二级': return 'warning';
    case '三级': return 'primary';
    default: return 'info';
  }
};

// 在script部分添加状态转换方法
const getStatusTagType = (status: string) => {
  switch(status) {
    case '0': return 'info';    // 不予受理
    case '-1': return '';       // 待受理
    case '1': return 'primary'; // 办理中
    case '2': return 'warning'; // 待核查
    case '3': return 'success'; // 已结案
    default: return 'info';
  }
};

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '0': '不予受理',
    '-1': '待受理',
    '1': '办理中',
    '2': '待核查',
    '3': '已结案'
  };
  return statusMap[status] || status;
};
</script>

<style lang="scss" scoped>
.mod-vsafety__gridinfo {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  height: calc(100vh - 84px);
  display: flex;
  flex-direction: column;
  
  .action-bar {
    margin-bottom: 20px;
    
    .el-form {
      display: flex;
      align-items: center;
      
      .el-form-item {
        margin-bottom: 0;
        margin-right: 10px;
      }
    }
  }
  
  .card-container {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    overflow-y: auto;
    padding: 10px;
    margin-bottom: 20px;
    
    .info-card {
      border: 1px solid #ebeef5;
      border-radius: 4px;
      padding: 15px;
      background-color: #fff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      transition: all 0.3s;
      display: flex;
      flex-direction: column;
      
      &.selected {
        border: 2px solid #409eff;
        background-color: #f0f7ff;
      }
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
      }
      
      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
        
        .el-checkbox {
          margin-right: 10px;
        }
        
        .serial-number {
          flex: 1;
          font-weight: bold;
          margin-right: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .el-tag {
          margin-left: 5px;
        }
      }
      
      .card-content {
        flex: 1;
        
        .info-item {
          margin-bottom: 8px;
          display: flex;
          
          .label {
            width: 80px;
            color: #909399;
            font-size: 13px;
          }
          
          .value {
            flex: 1;
            font-size: 13px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
      
      .card-footer {
        display: flex;
        justify-content: flex-end;
        margin-top: 15px;
        padding-top: 10px;
        border-top: 1px solid #ebeef5;
        
        .el-button {
          margin-left: 10px;
          padding: 5px 10px;
        }
      }
    }
  }
  
  .el-pagination {
    justify-content: flex-end;
  }
}
</style>
<style lang="scss">
.detail-dialog {
  .el-dialog__body {
    padding: 10px 20px;
  }
  
  .detail-container {
    .detail-section {
      margin-bottom: 20px;
      
      .el-card__header {
        padding: 10px 20px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #ebeef5;
      }
      
      .section-header {
        display: flex;
        align-items: center;
        font-weight: 600;
        color: #303133;
        
        .el-icon {
          margin-right: 8px;
          font-size: 16px;
        }
      }
      
      .el-descriptions {
        .el-descriptions__label {
          width: 180px;
          font-weight: 500;
        }
        
        .el-descriptions__content {
          word-break: break-all;
        }
      }
      
      .text-content {
        white-space: pre-wrap;
        line-height: 1.6;
        max-height: 200px;
        overflow-y: auto;
        padding: 8px;
        background-color: #f5f7fa;
        border-radius: 4px;
      }
      
      .status-hint {
        color: #909399;
        font-size: 12px;
        margin-left: 5px;
      }
    }
  }
}
</style>