<template>
  <div class="common-layout">
    <div class="common-header">
      <el-input v-model="search" style="max-width: 630px" clearable placeholder="请输入企业名称、企业简称、产品名、行业名称、政策法规、人名等进行搜索">
        <template #append><el-button size="large" class="search-btn" @click="searchData">全文搜索</el-button></template>
      </el-input>
      <!-- <div style="margin-top:-30px;margin-left:390px;cursor:pointer;color:#9ca0ac">
        <el-icon size="large"><Picture /></el-icon>
      </div> -->
      <el-tooltip class="box-item" effect="light" :show-arrow="false" content="按图片搜索" placement="bottom-end">
        <el-icon size="large" @click="dialogTableVisible = true" class="search-icon"><Camera /></el-icon>
      </el-tooltip>
    </div>
    <el-container>
      <el-header>
        <div class="header-content">
          <div class="industry-label">涉及行业</div>
          <el-radio-group v-model="checkboxGroup1" size="small">
            <el-space wrap>
              <el-radio-button @change="changeIndustry('')" @click="getByItem" value="999" style="margin-top: 5px"> 全部 </el-radio-button>
              <el-radio-button @change="changeIndustry(item.id)" @click="getByItem" v-for="item in industryList" :key="item.id" :value="item.id"> {{ item.name }} </el-radio-button>
            </el-space>
          </el-radio-group>
        </div>
        <div class="header-content">
          <div class="industry-label">涉及事件</div>
          <el-checkbox-group v-model="checkboxGroup2" size="small">
            <el-space wrap>
              <el-checkbox-button @click="loadAllEvent" value="999" style="margin-top: 5px"> 全部 </el-checkbox-button>
              <el-checkbox-button @click="changeEvent(item.id)" v-for="item in eventList" :key="item.id" :value="item.id"> {{ item.name }} </el-checkbox-button>
            </el-space>
          </el-checkbox-group>
        </div>
        <div class="header-content">
          <div class="industry-label">涉及省份</div>
          <el-radio-group v-model="checkboxGroup3" size="small">
            <el-space wrap>
              <el-radio-button @change="changeProvince('')" @click="getByItem" value="999" style="margin-top: 5px"> 全部 </el-radio-button>
              <el-radio-button @change="changeProvince(item.id)" @click="getByItem" v-for="item in provinceList" :key="item.id" :value="item.id">
                {{ item.name }}
              </el-radio-button>
            </el-space>
          </el-radio-group>
        </div>
        <div class="header-content">
          <div class="industry-label">涉及城市</div>
          <el-checkbox-group v-model="checkboxGroup4" size="small">
            <el-space wrap>
              <el-checkbox-button @click="loadAllCity" value="999" style="margin-top: 5px"> 全部 </el-checkbox-button>
              <el-checkbox-button @click="changeCity(item.id)" v-for="item in cityList.slice(0, 23)" style="margin-top: 5px" :key="item.id" :value="item.id">
                {{ item.name }}
              </el-checkbox-button>
            </el-space>
          </el-checkbox-group>
        </div>
      </el-header>
      <el-main v-loading="loading">
        <div v-show="detailList.length <= 0" style="text-align: center">暂无数据 !</div>
        <div v-for="item in detailList" :key="item.id" class="detail-item">
          <div class="detail-title">
            <div class="detail-title-left" @click="to(item.url)">{{ item.name }}</div>
            <span style="padding: 0 100px">
              <el-tag type="primary">{{ getIndustry(item.industryId) }}</el-tag> <el-tag type="success">{{ getEvent(item.evenId) }}</el-tag></span
            >
          </div>
          <!--          <div class="detail-content">
            {{ item.content.substring(0, 60) }}<span :style="{ color: getColor(item.natureId) }">{{ getNature(item.natureId) }}</span>
          </div>-->
          <div class="detail-footer">
            来源：{{ item.source }} 作者：{{ item.author }} {{ getProvince(item.provinceId) }} <span style="padding: 0 10px">{{ item.time }}</span>
          </div>
          <el-divider />
        </div>
      </el-main>
    </el-container>
  </div>
  <div class="demo-pagination-block">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :size="size"
      :disabled="disabled"
      :background="background"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
  <el-dialog v-model="dialogTableVisible" title="上传图片" width="500" style="height: 300px">
    <el-upload class="upload-demo" drag limit="1" :on-change="change" :action="''">
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">拖动文件到此处 <em>或点击上传</em></div>
    </el-upload>
    <!-- <el-button type="primary" plain @click="imageSearch" class="image-search-btn">搜索</el-button> -->
  </el-dialog>
</template>

<script setup>
import router from "@/router";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { ref } from "vue";
const loading = ref(true);
const dialogTableVisible = ref(false);
const industryList = ref([]);
const eventList = ref([]);
const provinceList = ref([]);
//全文搜索
const search = ref("");
//默认第一页
const currentPage = ref(1);
//默认每页显示10条
const pageSize = ref(10);
const size = ref("default");
const background = ref(true);
const disabled = ref(false);
const total = ref(0);
const cityList = ref([]);
const detailList = ref([]);
const natureList = ref([]);
const getByItem = () => {
  loading.value = true;
  const req1 = ref([]);
  const req2 = ref([]);
  const req3 = ref([]);
  setTimeout(() => {
    if (checkboxGroup1.value == "999" && checkboxGroup3.value == "999" && checkboxGroup2.value[0] === "999" && checkboxGroup4.value[0] === "999") {
      findAllDetail();
      return;
    }
    if (checkboxGroup2.value[0] === "999") {
      eventList.value.forEach((item) => {
        req1.value.push(item.id);
      });
    } else {
      req1.value = checkboxGroup2.value;
    }
    if (checkboxGroup4.value[0] === "999") {
      cityList.value.forEach((item) => {
        req2.value.push(item.id);
      });
    } else {
      req2.value = checkboxGroup4.value;
    }
    if (checkboxGroup1.value == "999") {
      industryList.value.forEach((item) => {
        req3.value.push(item.id);
      });
    } else {
      req3.value = checkboxGroup1.value;
    }
    baseService.get("es/byItems", { evenIds: req1.value.toString(), cityIds: req2.value.toString(), industryIds: req3.value.toString(), page: currentPage.value, size: pageSize.value }).then((res) => {
      detailList.value = res.data.list;
      total.value = res.data.total;
      loading.value = false;
    });
  }, 500);
};
const getNature = (natureId) => {
  return natureList.value.find((item) => item.id == natureId)?.name;
};
const getEvent = (eventId) => {
  return eventList.value.find((item) => item.id == eventId)?.name;
};
const getIndustry = (industryId) => {
  return industryList.value.find((item) => item.id == industryId)?.name;
};
const getProvince = (provinceId) => {
  return provinceList.value.find((item) => item.id == provinceId)?.name;
};
const getColor = (natureId) => {
  const color = {
    1: "orange",
    2: "red",
    3: "green",
    4: "blue"
  };
  return color[natureId];
};
Promise.all([baseService.get("vsafety/industry/page", { limit: 100 }), baseService.get("vsafety/province/page", { limit: 100 }), baseService.get("vsafety/city/page", { limit: 100 }), baseService.get("vsafety/event/page", { limit: 100 }), baseService.get("/vsafety/nature/page")]).then(
  ([industryRes, provinceRes, cityRes, eventRes, natureRes]) => {
    industryList.value = industryRes.data.list;
    provinceList.value = provinceRes.data.list;
    cityList.value = cityRes.data.list;
    eventList.value = eventRes.data.list;
    natureList.value = natureRes.data.list;
    loading.value = false;
  }
);

const findAllDetail = () => {
  baseService.get("es", { page: currentPage.value, size: pageSize.value }).then((res) => {
    detailList.value = res.data.list;
    total.value = res.data.total;
    loading.value = false;
  });
};
findAllDetail();
const checkboxGroup1 = ref(999);
const checkboxGroup2 = ref(["999"]);
const checkboxGroup3 = ref(999);
const checkboxGroup4 = ref(["999"]);
// 异步获取城市列表
const getCityList = (provinceId) => {
  baseService.get("yuqing/city/page", { provinceId, limit: 100 }).then((res) => {
    cityList.value = res.data.list;
  });
};
//跳转到详情页
const to = (url) => {
  router.push({ path: "/yuqing/detail", state: { url } });
};
const changeIndustry = (industryId) => {
  checkboxGroup2.value = ["999"];
};
const changeProvince = (provinceId) => {
  checkboxGroup4.value = ["999"];
  getCityList(provinceId);
};
const changeEvent = (eventId) => {
  getByItem();
  checkboxGroup2.value = checkboxGroup2.value.filter((item) => item !== "999");
};
const changeCity = (cityId) => {
  getByItem();
  checkboxGroup4.value = checkboxGroup4.value.filter((item) => item !== "999");
};
const loadAllEvent = () => {
  getByItem();
  checkboxGroup2.value = [];
};
const loadAllCity = () => {
  getByItem();
  checkboxGroup4.value = [];
};
//分页查询组件
const handleSizeChange = (val) => {
  baseService.get("es", { value: search.value.trim(), page: currentPage.value, size: val }).then((res) => {
    detailList.value = res.data.list;
    // total.value = res.data.total;
  });
};
const handleCurrentChange = (val) => {
  baseService.get("es", { value: search.value.trim(), page: val, size: pageSize.value }).then((res) => {
    detailList.value = res.data.list;
    // total.value = res.data.total;
  });
};
//图片搜索
const imageSearch = () => {
  dialogTableVisible.value = false;
};
//全文搜索
const searchData = () => {
  loading.value = true;
  //清空选择条件
  checkboxGroup1.value = "999";
  checkboxGroup2.value = ["999"];
  checkboxGroup3.value = "999";
  checkboxGroup4.value = ["999"];
  if (search.value.trim() === "") {
    findAllDetail();
    ElMessage.success("搜索成功");
    return;
  }
  baseService.get("es", { value: search.value.trim(), page: currentPage.value, size: pageSize.value }).then((res) => {
    if (res.code == 0) {
      ElMessage.success("搜索成功");
    }
    detailList.value = res.data.list;
    total.value = res.data.total;
    loading.value = false;
  });
};
import { Plus } from "@element-plus/icons-vue";
const imageUrl = ref("");

const change = (file) => {
  if (file.raw.type === "image/jpeg" || file.raw.type === "image/png") {
    TODO: 这里上传图片到服务器算法识别;
  } else {
    ElMessage.error("文件格式不正确！");
  }
};
</script>

<style scoped>
.el-header {
  background-color: #ebebeb80;
  padding: 10px 15px;
  height: 100%;
  line-height: 3;
}
.el-main {
  margin-top: 50px;
  background-color: #ebebeb80;
}
.demo-button-style {
  position: relative;
  top: -33px;
  left: 80px;
}
.demo-button-style {
  margin-top: 24px;
}
.header-content {
  display: flex;
  align-items: center;
}
.industry-label {
  margin-right: 10px;
  min-width: 70px;
}
.detail-item {
  /* margin-top: 20px; */
  padding: 10px;
  border-radius: 5px;
  /* background-color: #fff; */
}
.detail-title {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}
.detail-content {
  font-size: 14px;
  line-height: 2;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}
.detail-footer {
  font-size: 12px;
  color: #999;
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
}
.el-tag {
  margin-right: 20px;
  width: 80px;
}
.detail-title-left {
  cursor: pointer;
  color: #677fef;
}
.detail-title-left:hover {
  color: #4060ee;
}
.demo-pagination-block + .demo-pagination-block {
  margin-top: 10px;
}
.demo-pagination-block .demonstration {
  margin-bottom: 16px;
}
.common-header {
  text-align: center;
  margin-bottom: 30px;
}
.search-btn {
  background-color: #5b4cfe !important;
  color: #fff !important;
  border-radius: 0 5px 5px 0 !important;
}
.search-btn:hover {
  background-color: #7f71ff !important;
}
.search-icon {
  cursor: pointer;
  color: #9ca0ac;
  position: relative;
  top: 5px;
  margin-left: -125px;
}
.search-icon:hover {
  color: #4060ee;
}
.image-search-btn {
  margin-top: 10px;
  margin-left: 85%;
}
</style>
