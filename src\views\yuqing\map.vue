<template>
  <div id="chart-panel" style="width: 100%; height: 600px"></div>
</template>

<script setup>
import * as echarts from "echarts";
import { onMounted, ref } from "vue";
import chinaJson from "@/assets/中华人民共和国.json"; // 确保路径正确
import baseService from "@/service/baseService";
const detail = ref([]);
baseService.get("es",{size:1}).then((res) => {
  detail.value = res.data.content;
});
const getData = (id) => {
  var num = 0;
  detail.value.forEach((item) => {
    if (item.provinceId == id) {
      num += 1;
    }
  });
  return num;
};

const getProvinceId = (name) => {
  const province = {
    北京市: "1",
    天津市: "2",
    上海市: "3",
    重庆市: "4",
    河北省: "5",
    山西省: "6",
    辽宁省: "7",
    吉林省: "8",
    黑龙江省: "9",
    江苏省: "10",
    浙江省: "11",
    安徽省: "12",
    福建省: "13",
    江西省: "14",
    山东省: "15",
    河南省: "16",
    湖北省: "17",
    湖南省: "18",
    广东省: "19",
    广西壮族自治区: "20",
    海南省: "21",
    四川省: "22",
    贵州省: "23",
    云南省: "24",
    西藏自治区: "25",
    陕西省: "26",
    甘肃省: "27",
    青海省: "28",
    宁夏回族自治区: "29",
    新疆维吾尔自治区: "30",
    香港特别行政区: "31",
    澳门特别行政区: "32",
    台湾: "33"
  };
  return province[name] || "0";
};

onMounted(() => {
  const myChart = echarts.init(document.getElementById("chart-panel"));
  const mapName = "china";
  echarts.registerMap(mapName, chinaJson);
  const option = {
    backgroundColor: "transparent", // 透明背景
    tooltip: {
      show: false,
      confine: true
    },
    geo: {
      map: mapName,
      roam: false,
      itemStyle: {
        normal: {
          areaColor: "#1d6eb7" // 地图显示颜色
        },
        emphasis: {
          areaColor: "#0a2dad" // 鼠标经过显示颜色
        }
      },
      label: {
        emphasis: {
          show: true,
          color: "yellow" // 鼠标经过状态的省份名称颜色
        }
      },
      top: 0,
      tooltip: {
        show: true,
        confine: true,
        backgroundColor: "rgba(0, 0, 0, 0.7)",
        // 字体颜色
        textStyle: {
          color: "#fff"
        },
        formatter: function (params) {
          return ["城市：" + params.name, "数值：" + getData(getProvinceId(params.name))].join("<br>");
        }
      }
    }
  };

  myChart.setOption(option);
});
</script>

<style scoped>
#chart-panel {
  width: 100%;
  height: 600px; /* 根据需要调整高度 */
  color: rgba(0, 0, 0, 0.74);
}
</style>
