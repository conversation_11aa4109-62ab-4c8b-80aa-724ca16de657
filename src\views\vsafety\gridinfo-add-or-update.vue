<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false" width="1000px">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="200px">
          <el-form-item label="创建人" prop="createBy">
        <el-input v-model="dataForm.createBy" placeholder="创建人"></el-input>
      </el-form-item>
          <el-form-item label="创建时间" prop="createTime">
        <el-input v-model="dataForm.createTime" placeholder="创建时间"></el-input>
      </el-form-item>
          <el-form-item label="更新人
" prop="updateBy">
        <el-input v-model="dataForm.updateBy" placeholder="更新人
"></el-input>
      </el-form-item>
          <el-form-item label="更新时间" prop="updateTime">
        <el-input v-model="dataForm.updateTime" placeholder="更新时间"></el-input>
      </el-form-item>
          <el-form-item label="事件名称" prop="subject">
        <el-input v-model="dataForm.subject" placeholder="事件名称"></el-input>
      </el-form-item>
          <el-form-item label="发生地点" prop="occurLocation">
        <el-input v-model="dataForm.occurLocation" placeholder="发生地点"></el-input>
      </el-form-item>
          <el-form-item label="经度" prop="lng">
        <el-input v-model="dataForm.lng" placeholder="经度"></el-input>
      </el-form-item>
          <el-form-item label="纬度" prop="lat">
        <el-input v-model="dataForm.lat" placeholder="纬度"></el-input>
      </el-form-item>
          <el-form-item label="发生网络ID" prop="occurGrid">
        <el-input v-model="dataForm.occurGrid" placeholder="发生网络ID"></el-input>
      </el-form-item>
          <el-form-item label="发生时间" prop="occurTime">
        <el-input v-model="dataForm.occurTime" placeholder="发生时间"></el-input>
      </el-form-item>
          <el-form-item label="是否重大" prop="isImportant">
        <el-input v-model="dataForm.isImportant" placeholder="是否重大"></el-input>
      </el-form-item>
          <el-form-item label="是否紧急" prop="isUrgent">
        <el-input v-model="dataForm.isUrgent" placeholder="是否紧急"></el-input>
      </el-form-item>
          <el-form-item label="事件单号" prop="serialNumber">
        <el-input v-model="dataForm.serialNumber" placeholder="事件单号"></el-input>
      </el-form-item>
          <el-form-item label="事件来源" prop="source">
        <el-input v-model="dataForm.source" placeholder="事件来源"></el-input>
      </el-form-item>
          <el-form-item label="创建部门" prop="createDept">
        <el-input v-model="dataForm.createDept" placeholder="创建部门"></el-input>
      </el-form-item>
          <el-form-item label="事件状态（0-不予受理  -1-待受理  1-办理中  2-待核查  3-已结案）" prop="issueStatus" style="margin-bottom: 180;">
        <el-input v-model="dataForm.issueStatus" placeholder="事件状态（0-不予受理  -1-待受理  1-办理中  2-待核查  3-已结案）"></el-input>
      </el-form-item>
          <el-form-item label="所属网络" prop="deptId">
        <el-input v-model="dataForm.deptId" placeholder="所属网络"></el-input>
      </el-form-item>
          <el-form-item label="附件" prop="attachment">
        <el-input v-model="dataForm.attachment" placeholder="附件"></el-input>
      </el-form-item>
          <el-form-item label="有无附件" prop="hasAttachment">
        <el-input v-model="dataForm.hasAttachment" placeholder="有无附件"></el-input>
      </el-form-item>
          <el-form-item label="是否限时办结" prop="hasDeadline">
        <el-input v-model="dataForm.hasDeadline" placeholder="是否限时办结"></el-input>
      </el-form-item>
          <el-form-item label="是否锁定" prop="isLocked">
        <el-input v-model="dataForm.isLocked" placeholder="是否锁定"></el-input>
      </el-form-item>
          <el-form-item label=" 录入人员名称" prop="enterName">
        <el-input v-model="dataForm.enterName" placeholder=" 录入人员名称"></el-input>
      </el-form-item>
          <el-form-item label="结案时间" prop="doneTime">
        <el-input v-model="dataForm.doneTime" placeholder="结案时间"></el-input>
      </el-form-item>
          <el-form-item label="协同单号" prop="collaborationNo">
        <el-input v-model="dataForm.collaborationNo" placeholder="协同单号"></el-input>
      </el-form-item>
          <el-form-item label="协同来源" prop="collaborationSource">
        <el-input v-model="dataForm.collaborationSource" placeholder="协同来源"></el-input>
      </el-form-item>
          <el-form-item label="事件等级" prop="level">
        <el-input v-model="dataForm.level" placeholder="事件等级"></el-input>
      </el-form-item>
          <el-form-item label="删除状态（0-正常， 1-已删除）" prop="delFlag">
        <el-input v-model="dataForm.delFlag" placeholder="删除状态（0-正常， 1-已删除）"></el-input>
      </el-form-item>
          <el-form-item label="质量评析" prop="qualityAnalysis">
        <el-input v-model="dataForm.qualityAnalysis" placeholder="质量评析"></el-input>
      </el-form-item>
          <el-form-item label="最近办理时间" prop="lastProcessTime">
        <el-input v-model="dataForm.lastProcessTime" placeholder="最近办理时间"></el-input>
      </el-form-item>
          <el-form-item label="催办人id" prop="uringPersonId">
        <el-input v-model="dataForm.uringPersonId" placeholder="催办人id"></el-input>
      </el-form-item>
          <el-form-item label="黄牌督办人id" prop="yellowSupervise">
        <el-input v-model="dataForm.yellowSupervise" placeholder="黄牌督办人id"></el-input>
      </el-form-item>
          <el-form-item label="黄牌督办次数" prop="yellowSuperviseCount">
        <el-input v-model="dataForm.yellowSuperviseCount" placeholder="黄牌督办次数"></el-input>
      </el-form-item>
          <el-form-item label="红牌督办人id" prop="redSupervise">
        <el-input v-model="dataForm.redSupervise" placeholder="红牌督办人id"></el-input>
      </el-form-item>
          <el-form-item label="是否超时（0-未超时 1-已超时）" prop="isTimeout">
        <el-input v-model="dataForm.isTimeout" placeholder="是否超时（0-未超时 1-已超时）"></el-input>
      </el-form-item>
          <el-form-item label="事件类型编码" prop="categoryCode">
        <el-input v-model="dataForm.categoryCode" placeholder="事件类型编码"></el-input>
      </el-form-item>
          <el-form-item label="事件类型名称" prop="categoryName">
        <el-input v-model="dataForm.categoryName" placeholder="事件类型名称"></el-input>
      </el-form-item>
          <el-form-item label="创建部门名称" prop="createDeptName">
        <el-input v-model="dataForm.createDeptName" placeholder="创建部门名称"></el-input>
      </el-form-item>
          <el-form-item label="当前部门组织编码" prop="currentDeptCode">
        <el-input v-model="dataForm.currentDeptCode" placeholder="当前部门组织编码"></el-input>
      </el-form-item>
          <el-form-item label="当前办理部门名称" prop="currentDeptName">
        <el-input v-model="dataForm.currentDeptName" placeholder="当前办理部门名称"></el-input>
      </el-form-item>
          <el-form-item label="坐标系类型" prop="coordinateType">
        <el-input v-model="dataForm.coordinateType" placeholder="坐标系类型"></el-input>
      </el-form-item>
          <el-form-item label="是否作废申请中（0-否，1-是）" prop="isBeingInvalidated">
        <el-input v-model="dataForm.isBeingInvalidated" placeholder="是否作废申请中（0-否，1-是）"></el-input>
      </el-form-item>
          <el-form-item label="是否领导批示事件（0-否，1是）" prop="isInstructions">
        <el-input v-model="dataForm.isInstructions" placeholder="是否领导批示事件（0-否，1是）"></el-input>
      </el-form-item>
          <el-form-item label="事件描述" prop="issueDescription">
        <el-input v-model="dataForm.issueDescription" placeholder="事件描述"></el-input>
      </el-form-item>
          <el-form-item label="是否有民转刑，刑转命风险" prop="isThereRiskCivilToCriminal">
        <el-input v-model="dataForm.isThereRiskCivilToCriminal" placeholder="是否有民转刑，刑转命风险"></el-input>
      </el-form-item>
          <el-form-item label="前序ID" prop="precedeId">
        <el-input v-model="dataForm.precedeId" placeholder="前序ID"></el-input>
      </el-form-item>
          <el-form-item label="后续ID" prop="subsequentId">
        <el-input v-model="dataForm.subsequentId" placeholder="后续ID"></el-input>
      </el-form-item>
          <el-form-item label="类别" prop="category">
        <el-input v-model="dataForm.category" placeholder="类别"></el-input>
      </el-form-item>
          <el-form-item label="风险值" prop="riskValue">
        <el-input v-model="dataForm.riskValue" placeholder="风险值"></el-input>
      </el-form-item>
          <el-form-item label="涉及人员" prop="personnelInvolved">
        <el-input v-model="dataForm.personnelInvolved" placeholder="涉及人员"></el-input>
      </el-form-item>
          <el-form-item label="事件链" prop="genre">
        <el-input v-model="dataForm.genre" placeholder="事件链"></el-input>
      </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '',  createBy: '',  createTime: '',  updateBy: '',  updateTime: '',  subject: '',  occurLocation: '',  lng: '',  lat: '',  occurGrid: '',  occurTime: '',  isImportant: '',  isUrgent: '',  serialNumber: '',  source: '',  createDept: '',  issueStatus: '',  deptId: '',  attachment: '',  hasAttachment: '',  hasDeadline: '',  isLocked: '',  enterName: '',  doneTime: '',  collaborationNo: '',  collaborationSource: '',  level: '',  delFlag: '',  qualityAnalysis: '',  lastProcessTime: '',  uringPersonId: '',  yellowSupervise: '',  yellowSuperviseCount: '',  redSupervise: '',  isTimeout: '',  categoryCode: '',  categoryName: '',  createDeptName: '',  currentDeptCode: '',  currentDeptName: '',  coordinateType: '',  isBeingInvalidated: '',  isInstructions: '',  issueDescription: '',  isThereRiskCivilToCriminal: '',  precedeId: '',  subsequentId: '',  category: '',  riskValue: '',  personnelInvolved: '',  genre: ''});

const rules = ref({
          createBy: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          createTime: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          updateBy: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          updateTime: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          subject: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          occurLocation: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          lng: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          lat: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          occurGrid: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          occurTime: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          isImportant: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          isUrgent: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          serialNumber: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          source: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          createDept: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          issueStatus: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          deptId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          attachment: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          hasAttachment: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          hasDeadline: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          isLocked: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          enterName: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          doneTime: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          collaborationNo: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          collaborationSource: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          level: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          delFlag: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          qualityAnalysis: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          lastProcessTime: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          uringPersonId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          yellowSupervise: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          yellowSuperviseCount: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          redSupervise: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          isTimeout: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          categoryCode: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          categoryName: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          createDeptName: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          currentDeptCode: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          currentDeptName: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          coordinateType: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          isBeingInvalidated: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          isInstructions: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          issueDescription: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          isThereRiskCivilToCriminal: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          precedeId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          subsequentId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          category: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          riskValue: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          personnelInvolved: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          genre: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ]
  });

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/vsafety/gridinfo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/vsafety/gridinfo", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
<style scoped>
.el-dialog {
  width: 600px;
}
</style>