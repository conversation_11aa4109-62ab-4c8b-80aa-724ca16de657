<template>
  <div class="rr-login">
    <!-- 粒子背景 -->
    <div class="particles-container">
      <div class="particle" v-for="n in 120" :key="n" :style="getParticleStyle()"></div>
    </div>

    <!-- 科技网格背景 -->
    <div class="tech-grid"></div>

    <!-- 主容器 -->
    <div class="rr-login-wrap">
      <!-- 左侧背景图片区域 -->
      <div class="rr-login-left hidden-sm-and-down">
        <div class="rr-login-left-title">舆情监测系统</div>
        <el-image :src="left_img" class="rr-login-left-logo" />
      </div>

      <!-- 右侧登录表单区域 -->
      <div class="rr-login-right">
        <div class="rr-login-right-main">
          <h4 class="rr-login-right-main-title">登录</h4>
          <el-form ref="formRef" label-width="0" :status-icon="true" :model="login" :rules="rules" @keyup.enter="onLogin">
            <el-form-item prop="username">
              <el-input
                v-model="login.username"
                size="large"
                placeholder="请输入用户账号"
                prefix-icon="user"
                autocomplete="off"
                class="tech-input">
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                placeholder="请输入用户密码"
                v-model="login.password"
                size="large"
                prefix-icon="lock"
                autocomplete="off"
                show-password
                class="tech-input">
              </el-input>
            </el-form-item>
            <el-form-item prop="captcha">
              <div class="captcha-wrapper">
                <el-input
                  v-model="login.captcha"
                  size="large"
                  placeholder="验证码"
                  prefix-icon="first-aid-kit"
                  class="tech-input captcha-input">
                </el-input>
                <img
                  class="captcha-image"
                  :src="state.captchaUrl"
                  @click="onRefreshCode"
                  alt="验证码" />
              </div>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                :disabled="state.loading"
                @click="onLogin"
                class="tech-login-btn"
                :loading="state.loading">
                登录
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import { CacheToken } from "@/constants/cacheKey";
import baseService from "@/service/baseService";
import { setCache } from "@/utils/cache";
import { ElMessage } from "element-plus";
import { getUuid } from "@/utils/utils";
import app from "@/constants/app";
import SvgIcon from "@/components/base/svg-icon/index";
import { useAppStore } from "@/store";
import { useRouter } from "vue-router";
import left_img from "@/assets/images/2.png";

const store = useAppStore();
const router = useRouter();

// 生成随机粒子样式 - 增加粒子数量和动画速度
const getParticleStyle = () => {
  return {
    left: Math.random() * 100 + '%',
    top: Math.random() * 100 + '%',
    animationDelay: Math.random() * 15 + 's',
    animationDuration: (Math.random() * 6 + 6) + 's'  // 6-12秒，比之前更快
  };
};

const state = reactive({
  captchaUrl: "",
  loading: false,
  year: new Date().getFullYear()
});

const login = reactive({ username: "", password: "", captcha: "", uuid: "" });

onMounted(() => {
  //清理数据
  store.logout();
  getCaptchaUrl();
});
const formRef = ref();

const rules = ref({
  username: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  password: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  captcha: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
});

const getCaptchaUrl = () => {
  login.uuid = getUuid();
  state.captchaUrl = `${app.api}/captcha?uuid=${login.uuid}`;
};

const onRefreshCode = () => {
  getCaptchaUrl();
};

const onLogin = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      state.loading = true;
      baseService
        .post("/login", login)
        .then((res) => {
          state.loading = false;
          if (res.code === 0) {
            // 登录成功逻辑
            setCache(CacheToken, res.data, true);
            ElMessage.success("登录成功");
            router.push("/");
          } else {
            login.captcha = ""; // 清空验证码
            onRefreshCode();    // 刷新验证码图片
            ElMessage.error(res.msg);
          }
        })
        .catch(() => {
          state.loading = false;
          login.captcha = ""; // 清空验证码
          onRefreshCode();   // 刷新验证码图片
        });
    }
  });
};
</script>

<style lang="less" scoped>
@import url("@/assets/theme/base.less");

.rr-login {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  // 使用淡蓝色背景
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 25%, #90caf9 50%, #64b5f6 75%, #42a5f5 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  // 粒子容器
  .particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
  }

  // 粒子样式 - 调整大小和颜色适配淡蓝色背景
  .particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    animation: float linear infinite;
    box-shadow: 0 0 12px rgba(255, 255, 255, 0.9);

    &:nth-child(odd) {
      background: rgba(25, 118, 210, 0.8);
      box-shadow: 0 0 12px rgba(25, 118, 210, 0.8);
      width: 3px;
      height: 3px;
    }

    &:nth-child(3n) {
      width: 2px;
      height: 2px;
      background: rgba(255, 255, 255, 0.7);
      box-shadow: 0 0 8px rgba(255, 255, 255, 0.7);
    }

    &:nth-child(4n) {
      width: 6px;
      height: 6px;
      background: rgba(13, 71, 161, 0.7);
      box-shadow: 0 0 15px rgba(13, 71, 161, 0.7);
      animation-duration: 8s;  // 更快的动画
    }

    &:nth-child(5n) {
      width: 3px;
      height: 3px;
      background: rgba(21, 101, 192, 0.6);
      box-shadow: 0 0 10px rgba(21, 101, 192, 0.6);
      animation-duration: 10s;
    }

    &:nth-child(6n) {
      width: 5px;
      height: 5px;
      background: rgba(30, 136, 229, 0.8);
      box-shadow: 0 0 14px rgba(30, 136, 229, 0.8);
      animation-duration: 12s;
    }
  }

  // 科技网格背景 - 调整颜色
  .tech-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    z-index: 1;
  }

  // 响应式设计
  @media only screen and (max-width: 992px) {
    .rr-login-wrap {
      width: 96% !important;
    }
    .rr-login-right {
      width: 100% !important;
    }
  }

  // 主容器
  &-wrap {
    margin: 0 auto;
    width: 1000px;
    animation-duration: 1s;
    animation-fill-mode: both;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    z-index: 10;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  // 左侧区域
  &-left {
    justify-content: center;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    color: #fff;
    float: left;
    width: 50%;
    position: relative;
    border-radius: 15px 0 0 15px;

    &-title {
      text-align: center;
      color: #1565c0;
      font-weight: 600;
      letter-spacing: 3px;
      font-size: 36px;
      margin-bottom: 40px;
      text-shadow: 0 2px 10px rgba(21, 101, 192, 0.3);
      background: linear-gradient(45deg, #1565c0, #1976d2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    &-logo {
      width: 80%;
      max-width: 400px;
      margin: 0 auto;
      border-radius: 10px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
  }

  // 右侧区域
  &-right {
    border-left: none;
    color: #333;
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(20px);
    width: 50%;
    float: left;
    border-radius: 0 15px 15px 0;

    &-main {
      margin: 0 auto;
      width: 75%;
      padding: 40px 0;

      &-title {
        color: #1565c0;
        margin-bottom: 40px;
        font-weight: 600;
        font-size: 28px;
        text-align: center;
        letter-spacing: 2px;
        text-shadow: 0 2px 10px rgba(21, 101, 192, 0.2);
      }
    }
  }

  // 左右区域共同样式
  &-left,
  &-right {
    position: relative;
    min-height: 500px;
    align-items: center;
    display: flex;
  }

  // 验证码包装器
  .captcha-wrapper {
    display: flex;
    gap: 12px;
    align-items: center;

    .captcha-input {
      flex: 1;
    }

    .captcha-image {
      width: 120px;
      height: 40px;
      border-radius: 8px;
      cursor: pointer;
      border: 2px solid rgba(21, 101, 192, 0.3);
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(21, 101, 192, 0.1);

      &:hover {
        border-color: rgba(21, 101, 192, 0.6);
        box-shadow: 0 4px 15px rgba(21, 101, 192, 0.2);
        transform: translateY(-1px);
      }
    }
  }

  // 科技感输入框 - 适配淡蓝色背景
  :deep(.tech-input) {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.4);
      border: 2px solid rgba(21, 101, 192, 0.2);
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(21, 101, 192, 0.1);
      transition: all 0.3s ease;

      &:hover {
        border-color: rgba(21, 101, 192, 0.4);
        background: rgba(255, 255, 255, 0.5);
        box-shadow: 0 4px 12px rgba(21, 101, 192, 0.15);
      }

      &.is-focus {
        border-color: #1976d2;
        background: rgba(255, 255, 255, 0.6);
        box-shadow:
          0 4px 12px rgba(21, 101, 192, 0.15),
          0 0 0 3px rgba(25, 118, 210, 0.1);
      }
    }

    .el-input__inner {
      color: #1565c0;
      font-size: 16px;
      font-weight: 500;

      &::placeholder {
        color: rgba(21, 101, 192, 0.6);
      }
    }

    .el-input__prefix-inner {
      color: rgba(21, 101, 192, 0.7);
    }

    .el-input__suffix-inner {
      color: rgba(21, 101, 192, 0.7);
    }
  }

  // 科技感登录按钮
  .tech-login-btn {
    width: 100%;
    height: 50px;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 2px;
    margin-top: 20px;
    background: linear-gradient(45deg, #1976d2, #40c4ff);
    border: none;
    border-radius: 12px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(64, 196, 255, 0.3);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(64, 196, 255, 0.4);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0);
    }

    &.is-loading {
      background: linear-gradient(45deg, #666, #888);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .login-container {
      max-width: 90%;
      padding: 10px;
    }

    .login-form-wrapper {
      padding: 30px 20px;
    }

    .system-title {
      font-size: 24px;
      margin-bottom: 30px;
    }
  }
}

// 动画定义
@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 覆盖 Element Plus 的默认样式
:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-form-item__error) {
  color: #ff6b6b;
  font-size: 12px;
  background: rgba(255, 107, 107, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  margin-top: 4px;
}
</style>
