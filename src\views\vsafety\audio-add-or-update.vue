<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="140px">
          <el-form-item label="音频链接" prop="url">
        <el-input v-model="dataForm.url" placeholder="音频链接"></el-input>
      </el-form-item>
          <el-form-item label="外键,关联平台表" prop="platformId">
        <el-input v-model="dataForm.platformId" placeholder="外键,关联平台表"></el-input>
      </el-form-item>
          <el-form-item label="对应事件id" prop="detailId">
        <el-input v-model="dataForm.detailId" placeholder="对应事件id"></el-input>
      </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '',  url: '',  platformId: '',  detailId: '', analysisResult: ''});

const rules = ref({
          url: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          platformId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          detailId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ]
  });

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  } else {
    // 新增时初始化分析结果为空
    dataForm.analysisResult = '';
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/vsafety/audio/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/vsafety/audio", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
