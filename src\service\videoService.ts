import baseService from './baseService';

/**
 * 获取指定 detailId 的视频 url 列表
 * @param detailId
 * @returns Promise<string[]>
 */
export function getVideoListByDetailId(detailId) {
  return new Promise((resolve, reject) => {
    baseService.get('vsafety/video/page', { id: detailId })
      .then(response => {
        const urls = (response.data.list || []).map(item => item.vidUrl);
        resolve(urls);
      })
      .catch(reject);
  });
} 