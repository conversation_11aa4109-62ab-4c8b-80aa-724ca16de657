<script setup >
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
const visible = ref(false);
const dataFormRef = ref();
const dataForm = reactive({
  id: '',
  subject: '',
  occurLocation: '',
  lng: null,
  lat: null,
  occurGrid: '',
  occurTime: null,
  isImportant: 1,
  isUrgent: 1,
  serialNumber: '',
  source: '',
  createDept: '',
  issueStatus: -1, // 默认待受理
  deptId: '',
  attachment: '',
  hasAttachment: 1,
  hasDeadline: 0,
  isLocked: 0,
  enterName: '',
  doneTime: null,
  collaborationNo: '',
  collaborationSource: '',
  level: null,
  delFlag: 0,
  qualityAnalysis: 0,
  lastProcessTime: null,
  uringPersonId: '',
  yellowSupervise: '',
  yellowSuperviseCount: 0,
  redSupervise: '',
  isTimeout: 0,
  categoryCode: '',
  categoryName: '',
  createDeptName: '',
  currentDeptCode: '',
  currentDeptName: '',
  coordinateType: '',
  isBeingInvalidated: 0,
  isInstructions: 0,
  issueDescription: '',
  isThereRiskCivilToCriminal: 0,
  precedeId: '',
  subsequentId: '',
  category: '',
  riskValue: null,
  personnelInvolved: '',
  genre: ''
})
const emit = defineEmits(['refreshDataList'])
const rules = ref({
  subject: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  occurLocation: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  issueDescription: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
});

const getInfo = (id) =>{
  baseService.get(`yuqing/grid/${id}`).then((res) =>{
    Object.assign(dataForm, res.data);
  })
}

const dataFormSubmitHandle = () =>{
  dataFormRef.value.validate((valid) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/yuqing/grid", dataForm).then((res) => {
      ElMessage.success({
        message: "成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
}

const init2 = (id) =>{
  // 修改
  visible.value = true;
  dataForm.id = "";
  // 重置表单数据到初始值
  if(dataFormRef.value){
    dataFormRef.value.resetFields();
  }
  getInfo(id);
}

const init = () => {
  visible.value = true

  dataForm.id = "";
  if(dataFormRef.value){
    dataFormRef.value.resetFields();
  }
}

defineExpose({init2,init})
</script>

<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'"  :show-close="true"  class="dialog-form" :close-on-click-modal="false">
    <div class="dialog-content" style="max-height: 70vh; overflow-y: auto;">
      <el-form  label-width="180px" :model="dataForm" :rules="rules" ref="dataFormRef">
        <el-form-item prop="subject" label="事件名称">
          <el-input v-model="dataForm.subject" placeholder="事件名称"></el-input>
        </el-form-item>
        <el-form-item prop="occurLocation" label="发生地点">
          <el-input v-model="dataForm.occurLocation" placeholder="事件发生地点"></el-input>
        </el-form-item>
        <el-form-item label="精度">
          <el-input v-model="dataForm.lng" placeholder="精度"></el-input>
        </el-form-item>
        <el-form-item label="纬度">
          <el-input v-model="dataForm.lat" placeholder="纬度"></el-input>
        </el-form-item>
        <el-form-item label="发生网格id">
          <el-input v-model="dataForm.occurGrid" placeholder="发生网格id"></el-input>
        </el-form-item>
        <el-form-item label="发生时间">
          <el-date-picker
            v-model="dataForm.occurTime"
            type="date"
            placeholder="发生时间"
            clearable
          />
        </el-form-item>
        <el-form-item label="是否重要">
          <el-radio-group v-model="dataForm.isImportant">
            <el-radio :value="1">是</el-radio>
            <el-radio :value="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否紧急">
          <el-radio-group v-model="dataForm.isUrgent">
            <el-radio :value="1">是</el-radio>
            <el-radio :value="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="事件单号">
          <el-input v-model="dataForm.serialNumber" placeholder="事件单号"></el-input>
        </el-form-item>
        <el-form-item label="事件来源">
          <el-input v-model="dataForm.source" placeholder="事件来源"></el-input>
        </el-form-item>
        <el-form-item label="创建部门">
          <el-input v-model="dataForm.createDept" placeholder="创建部门"></el-input>
        </el-form-item>
        <el-form-item label="事件状态">
          <el-radio-group v-model="dataForm.issueStatus">
            <el-radio :value="0">不予受理</el-radio>
            <el-radio :value="-1">待受理</el-radio>
            <el-radio :value="1">办理中</el-radio>
            <el-radio :value="2">待核</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="所属网格">
          <el-input v-model="dataForm.deptId" placeholder="所属网格"></el-input>
        </el-form-item>
        <el-form-item label="附件">
          <el-input v-model="dataForm.attachment" placeholder="附件"></el-input>
        </el-form-item>
        <el-form-item label="有无附件">
          <el-radio-group v-model="dataForm.hasAttachment">
            <el-radio :value="0">有</el-radio>
            <el-radio :value="1">无</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否限时办结">
          <el-radio-group v-model="dataForm.hasDeadline">
            <el-radio :value="0">是</el-radio>
            <el-radio :value="1">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否锁定">
          <el-radio-group v-model="dataForm.isLocked">
            <el-radio :value="0">是</el-radio>
            <el-radio :value="1">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="录入人员名称">
          <el-input v-model="dataForm.enterName" placeholder="录入人员名称"></el-input>
        </el-form-item>
        <el-form-item label="结案时间">
          <el-input v-model="dataForm.doneTime" placeholder="结案时间">
            <el-input-number v-model="dataForm.doneTime" placeholder="结案时间"></el-input-number>
          </el-input>
        </el-form-item>
        <el-form-item label="协作单号">
          <el-input v-model="dataForm.collaborationNo" placeholder="协作单号"></el-input>
        </el-form-item>
        <el-form-item label="协作来源">
          <el-input v-model="dataForm.collaborationSource" placeholder="协作来源"></el-input>
        </el-form-item>
        <el-form-item label="事件等级">
          <el-input v-model="dataForm.level" placeholder="事件等级"></el-input>
        </el-form-item>
        <el-form-item label="删除状态">
          <el-radio-group v-model="dataForm.delFlag">
            <el-radio :value="0">正常</el-radio>
            <el-radio :value="1">已删除</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="质量分析">
          <el-input v-model="dataForm.qualityAnalysis" placeholder="质量分析"></el-input>
        </el-form-item>
        <el-form-item label="最后处理时间">
          <el-date-picker
            v-model="dataForm.lastProcessTime"
            type="date"
            placeholder="最后处理时间"
            clearable
          />
        </el-form-item>
        <el-form-item label="催办人id">
          <el-input v-model="dataForm.uringPersonId" placeholder="预警人员id"></el-input>
        </el-form-item>
        <el-form-item label="黄色督办人id">
          <el-input v-model="dataForm.yellowSupervise" placeholder="黄色督办人id"></el-input>
        </el-form-item>
        <el-form-item label="黄色督办人次数">
          <el-input v-model="dataForm.yellowSuperviseCount" placeholder="黄色督办人次数"></el-input>
        </el-form-item>
        <el-form-item label="红色督办人id">
          <el-input v-model="dataForm.redSupervise" placeholder="红色督办人id"></el-input>
        </el-form-item>
        <el-form-item label="是否超时">
          <el-radio-group v-model="dataForm.isTimeout">
            <el-radio :value="0">未超时</el-radio>
            <el-radio :value="1">已超时</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="事件类别编码">
          <el-input v-model="dataForm.categoryCode" placeholder="事件类别编码"></el-input>
        </el-form-item>
        <el-form-item label="事件类别名称">
          <el-input v-model="dataForm.categoryName" placeholder="事件类别名称"></el-input>
        </el-form-item>
        <el-form-item label="创建部门名称">
          <el-input v-model="dataForm.createDeptName" placeholder="创建部门名称"></el-input>
        </el-form-item>
        <el-form-item label="当前部门编码">
          <el-input v-model="dataForm.currentDeptCode" placeholder="当前部门编码"></el-input>
        </el-form-item>
        <el-form-item label="当前部门名称">
          <el-input v-model="dataForm.currentDeptName" placeholder="当前部门名称"></el-input>
        </el-form-item>
        <el-form-item label="坐标类型">
          <el-input v-model="dataForm.coordinateType" placeholder="坐标类型"></el-input>
        </el-form-item>
        <el-form-item label="是否作废申请中">
          <el-radio-group v-model="dataForm.isBeingInvalidated">
            <el-radio :value="0">否</el-radio>
            <el-radio :value="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否领导批示事件">
          <el-radio-group v-model="dataForm.isInstructions">
            <el-radio :value="0">否</el-radio>
            <el-radio :value="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="事件描述" prop="issueDescription">
          <el-input v-model="dataForm.issueDescription" placeholder="事件描述"></el-input>
        </el-form-item>
        <el-form-item label="是否有民转刑, 刑转命风险">
          <el-radio-group v-model="dataForm.isThereRiskCivilToCriminal">
            <el-radio :value="0">否</el-radio>
            <el-radio :value="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="前事件id">
          <el-input v-model="dataForm.precedeId" placeholder="前事件id"></el-input>
        </el-form-item>
        <el-form-item label="后事件id">
          <el-input v-model="dataForm.subsequentId" placeholder="后事件id"></el-input>
        </el-form-item>
        <el-form-item label="事件类别">
          <el-input v-model="dataForm.category" placeholder="事件类别"></el-input>
        </el-form-item>
        <el-form-item label="事件风险值">
          <el-input v-model="dataForm.riskValue" placeholder="事件风险值"></el-input>
        </el-form-item>
        <el-form-item label="事件涉及人员">
          <el-input v-model="dataForm.personnelInvolved" placeholder="事件涉及人员"></el-input>
        </el-form-item>
        <el-form-item label="事件链">
          <el-input v-model="dataForm.genre" placeholder="事件链"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <template v-slot:footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<style scoped>

:deep(.el-dialog__body) {
  max-height: 60vh!important; 
  overflow-y: auto !important;/* 垂直滚动 */
  padding: 20px;
}

/* 如果需要美化滚动条 */
:deep(.el-dialog__body::-webkit-scrollbar) {
  width: 6px;
}

:deep(.el-dialog__body::-webkit-scrollbar-thumb) {
  background: #ddd;
  border-radius: 3px;
}
</style>
