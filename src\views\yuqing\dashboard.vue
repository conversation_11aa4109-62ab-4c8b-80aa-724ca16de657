<template>
  <div class="dashboard">
    <el-row :gutter="10" class="ep-row-wrap">
      <el-col :span="7"
        ><div class="grid-content1 ep-bg-purple1">
          <!-- 当前时间 -->
          <div class="ep-select">{{ now }}</div>
        </div></el-col
      >
      <el-col :span="10"><div class="grid-content1 ep-bg-purple2">舆情数据监测大屏</div></el-col>
      <el-col :span="7"><div class="grid-content1 ep-bg-purple3"></div></el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="7"
        ><div class="grid-content2 ep-bg-purple1">
          <div class="left-top">最新舆情</div>
          <div v-for="item in filterDetailList" :key="item.id" class="detail-item">
            <div class="detail-title">
              <div class="detail-title-left" @click="to(item.url)">{{ item.name.substring(0, 40) }}</div>
              <span class="detail-title-right" :style="{ color: getColor(item.natureId) }"
                ><span style="background-color: rgba(255, 255, 255, 0.562); padding: 2px; font-size: 12px">{{ getNature(item.natureId) }}</span></span
              >
            </div>
            <div class="detail-footer">{{ item.time }} {{ item.source }}</div>
          </div>
        </div></el-col
      >
      <el-col :span="10"
        ><div class="grid-content2 ep-bg-purple2">
          <el-row :gutter="20">
            <el-col :span="8"
              ><div class="grid-content22">
                <span class="ep-title" style="color: #00fffc">全部信息</span>
                <span style="color: #fff">——</span>
                <span class="ep-sum" style="color: #00fffc">{{ allCount }}</span>
              </div></el-col
            >
            <el-col :span="8"
              ><div class="grid-content22">
                <span class="ep-title" style="width: 55px; color: #fed52f">非消极信息</span>
                <span style="color: #fff">——</span>
                <span class="ep-sum" style="color: #fed52f">{{ allCount - badCount }}</span>
              </div></el-col
            >
            <el-col :span="8"
              ><div class="grid-content22">
                <span class="ep-title" style="color: #ea5b1c">消极信息</span>
                <span style="color: #fff">——</span>
                <span class="ep-sum" style="color: #ea5b1c">{{ badCount }}</span>
              </div></el-col
            >
            <div></div>
          </el-row>
          <maps></maps>
        </div></el-col
      >
      <el-col :span="7"
        ><div class="grid-content2 ep-bg-purple3">
          <div class="right-top">数据来源分析</div>
          <div style="color: white; font-size: 14px; margin-top: 20px; text-align: center">
            <table>
              <thead>
                <tr>
                  <th style="width: 200px">排名</th>
                  <th style="width: 200px">媒体名称</th>
                  <th style="width: 200px">媒体类型</th>
                  <th style="width: 200px">总条数</th>
                  <th style="width: 200px">负面舆情</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, key) in dataSources" :key="item.id">
                  <td>
                    <el-tag :style="{ backgroundColor: getDataColor(key + 1) }" style="width: 0px; border-color: transparent">{{ key + 1 }}</el-tag>
                  </td>
                  <td>{{ item.name }}</td>
                  <td>{{ item.type }}</td>
                  <td>{{ item.count }}</td>
                  <td style="color:red">{{ item.percent }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div></el-col
      >
    </el-row>
    <el-row :gutter="10">
      <el-col :span="7"
        ><div class="grid-content3 ep-bg-purple1">
          <div class="left-button">情感占比</div>
          <div id="main1" style="width: 100%; height: 300px; text-align: center; margin: 0 auto"></div></div
      ></el-col>
      <el-col :span="10"
        ><div class="grid-content3 ep-bg-purple2">
          <div class="center-button">热点事件</div>
          <div class="ep-hotpoint">
          <div style="color: white; font-size: 12px; margin-top: 10px; margin-left: 10px;margin-right: 10px; text-align: left; overflow: hidden; text-overflow: ellipsis; white-space: nowrap" v-for="item in detailList" :key="item.id">
            <span class="detail-title-right" :style="{ color: getColor(item.natureId) }"
              ><span style="padding: 2px; font-size: 12px">{{ getNature(item.natureId) }}</span></span
            >
            {{ item.source }} {{ item.time }} {{ item.name }}
          </div>
          </div>

        </div></el-col
      >
      <el-col :span="7"
        ><div class="grid-content3 ep-bg-purple3">
          <div class="right-button">事件统计</div>
          <div id="main2" style="width: 100%; height: 300px; text-align: center; margin: 0 auto"></div></div
      ></el-col>
    </el-row>
  </div>
</template>
<script setup>
import router from "@/router";
import baseService from "@/service/baseService";
import { onMounted, ref } from "vue";
import * as echarts from "echarts/core";
import { TooltipComponent, LegendComponent } from "echarts/components";
import { PieChart } from "echarts/charts";
import { GridComponent } from "echarts/components";
import { BarChart } from "echarts/charts";
import { LabelLayout } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
import maps from "@/views/yuqing/map.vue";
import { map } from "lodash";
const now = ref();
const allCount = ref(0);
const badCount = ref(0);
const goodCount = ref(0);
onMounted(() => {
var container = document.querySelector('.ep-hotpoint');
    var timer = setInterval(() => {
      container.scrollTop += 1
      if (container.scrollTop >= container.scrollHeight - container.offsetHeight -1) {
        container.scrollTop = 0
      }
    }, 100);
    container.addEventListener('mouseenter', () => {
      clearInterval(timer);
      container.style.cursor = "pointer";
      }
    )
    container.addEventListener('mouseleave', () => {
      timer = setInterval(() => {
        container.scrollTop += 1
        if (container.scrollTop >= container.scrollHeight - container.offsetHeight -1) {
          container.scrollTop = 0
        }
      }, 100);
      }
    )
})

const getNow = () => {
  setInterval(() => {
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1;
    const day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
    const hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
    const minute = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
    const second = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
    now.value = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  }, 1000);
};
// 获取当前时间
getNow();
echarts.use([TooltipComponent, LegendComponent, PieChart, CanvasRenderer, LabelLayout, GridComponent, BarChart]);
// var option1;
// var option2;

// option1 = {
//   tooltip: {
//     trigger: "item"
//   },
//   series: [
//     {
//       name: "",
//       type: "pie",
//       radius: ["40%", "70%"],
//       avoidLabelOverlap: false,
//       itemStyle: {
//         borderRadius: 10,
//         borderColor: "#fff",
//         borderWidth: 0
//       },
//       label: {
//         show: true
//       },
//       emphasis: {
//         label: {
//           show: false,
//           fontSize: 40,
//           fontWeight: "bold"
//         }
//       },
//       labelLine: {
//         show: false
//       },
//       data: [
//         { value: goodCount.value, name: "正面" },
//         { value: allCount.value - goodCount.value - badCount.value, name: "中性" },
//         { value: badCount.value, name: "负面" }
//       ]
//     }
//   ]
// };
// option2 = {
//   tooltip: {
//     trigger: "item"
//   },
//   xAxis: {
//     type: "category",
//     data: ["往事小说", "招标投标", "历史事件", "突发事件", "股市公告"],
//     axisLabel: {
//       color: "#fff",
//       fontSize: 9
//     }
//   },
//   yAxis: {
//     type: "value",
//     splitLine: {
//       show: false // 隐藏横向网格线
//     }
//   },
//   series: [
//     {
//       data: [5, 2, 1, 1, 1],
//       type: "bar",
//       itemStyle: {
//         color: "#ff7f50"
//       }
//     }
//   ]
// };
onMounted(() => {
  // var chartDom = document.getElementById("main1");
  // var chartDom2 = document.getElementById("main2");
  // var myChart = echarts.init(chartDom);
  // var myChart2 = echarts.init(chartDom2);
  // option1 && myChart.setOption(option1);
  // option2 && myChart2.setOption(option2);

});

const hotpoint = ref("");
const time = ref("");
const dataSources = ref()
baseService.get('es/source').then(res => {
  dataSources.value = res.data.sort((a, b) => b.count - a.count);
})
const getDataColor = (key) => {
  const color = {
    1: "red",
    2: "orange",
    3: "yellow"
  };
  return color[key];
};
const detailList = ref([]);
const filterDetailList = ref([]);
const natureList = ref([]);
const getColor = (natureId) => {
  const color = {
    1: "orange",
    2: "red",
    3: "green",
    4: "blue"
  };
  return color[natureId];
};
const getNature = (natureId) => {
  return natureList.value.find((item) => item.id == natureId).name;
};
//跳转到详情页
const to = (url) => {
  router.push({ path: "/vsafety/envdetail", state: { url } });
};
Promise.all([baseService.get("es",{size:1}), baseService.get("/vsafety/nature"), baseService.get('es/byEvenId',{evenId:1}),baseService.get('es/byEvenId',{evenId:2}),baseService.get('es/byEvenId',{evenId:3}),baseService.get('es/byEvenId',{evenId:4})]).then(([detailRes, natureRes, count1, count2, count3, count4]) => {
  const seriesData = [count1.data, count2.data, count3.data, count4.data];
  detailList.value = detailRes.data.content;
  allCount.value = detailRes.data.numberOfElements;
  badCount.value = detailRes.data.content.filter((item) => item.natureId == 2).length;
  goodCount.value = detailRes.data.content.filter((item) => item.natureId == 3).length;
  //按照时间倒序
  filterDetailList.value = detailList.value.slice(0, 4).sort((a, b) => new Date(b.time) - new Date(a.time));
  natureList.value = natureRes.data.list;
  echarts.init(document.getElementById("main1")).setOption({
  tooltip: {
    trigger: "item"
  },
  series: [
    {
      name: "",
      type: "pie",
      radius: ["40%", "70%"],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: "#fff",
        borderWidth: 0
      },
      label: {
        show: true
      },
      emphasis: {
        label: {
          show: false,
          fontSize: 40,
          fontWeight: "bold"
        }
      },
      labelLine: {
        show: false
      },
      data: [
        { value: goodCount.value, name: "正面" },
        { value: allCount.value - goodCount.value - badCount.value, name: "中性" },
        { value: badCount.value, name: "负面" }
      ]
    }
  ]
});
  echarts.init(document.getElementById("main2")).setOption({
  tooltip: {
    trigger: "item"
  },
  xAxis: {
    type: "category",
    data: ["教育行业", "医疗行业", "政府部门", "房产行业",],
    axisLabel: {
      color: "#fff",
      fontSize: 9
    }
  },
  yAxis: {
    type: "value",
    splitLine: {
      show: false // 隐藏横向网格线
    }
  },
  series: [
    {
      data: seriesData,
      type: "bar",
      itemStyle: {
        color: "#ff7f50"
      }
    }
  ]
});
});
</script>
<style scoped >
.el-row {
  margin-bottom: 20px;
}
.el-row:last-child {
  margin-bottom: 0;
}
.el-col {
  border-radius: 4px;
}

.grid-content1 {
  border-radius: 4px;
  min-height: 120px;
  /* background-color: rgba(255, 192, 203, 0.432); */
}
.grid-content2 {
  border-radius: 4px;
  /* min-height: 390px; */
  height: 490px;
  overflow: auto;
  /* background-color: rgba(255, 192, 203, 0.432); */
}
.grid-content3 {
  border-radius: 4px;
  min-height: 260px;
  /* max-height: 500px; */
  /* background-color: rgba(255, 192, 203, 0.432); */
}
/* 整体背景 */
.dashboard {
  background-color: #1d2b56;
  background-size: 100% 100%;
  background-image: url("@/assets/images/bg.png") !important;
}
/* 头部 */
.ep-row-wrap {
  background-image: url("../../assets/images/topbg.png");
  background-size: 98% 50%;
  /* 图片往上移 */

  margin-top: -55px;
  background-repeat: no-repeat;
  background-position: center;
}
/* 中间的左右框 */
.grid-content2.ep-bg-purple1,
.grid-content2.ep-bg-purple3 {
  background-image: url("../../assets/images/main-use.png");
  background-size: 100% 100%;
}
.grid-content3.ep-bg-purple2 {
  background-image: url("../../assets/images/footer.png");
  background-size: 100% 100%;
  margin-top: 50px;
}
.ep-hotpoint{
  /* background-color: pink; */
  height: 150px;
  overflow: auto;
  /* 隐藏滚动条 */
  scrollbar-width: none;
}
/* 底部的左右框 */
.grid-content3.ep-bg-purple1,
.grid-content3.ep-bg-purple3 {
  background-image: url("../../assets/images/main-use.png");
  background-size: 100% 100%;
}
.grid-content1.ep-bg-purple2 {
  font-size: 30px;
  color: white;
  text-align: center;
  padding-top: 40px;
  text-shadow: #b0b4c2 5px 5px 10px;
}
.grid-content2.ep-bg-purple2 {
  overflow: hidden;
}
.grid-content1.ep-bg-purple1 {
  background-image: url("../../assets/images/time.png");
  background-size: 230px 50px;
  background-repeat: no-repeat;
  /* background-position: center; */
  background-position-y: 63px;
}
/* 选择框 */
.ep-select {
  width: 250px;
  padding: 80px 0 0 50px;
  color: white;
  text-shadow: #b0b4c2 5px -5px 5px;
}
.ep-select.el-input__inner {
  background-color: transparent; /* 输入框背景透明 */
}
.detail-item {
  margin-top: 20px;
  padding: 10px;
  border-radius: 5px;
  /* background-color: #fff; */
}
.detail-title {
  font-size: 13px;
  /* font-weight: bold; */
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}
.detail-title-left {
  cursor: pointer;
  margin-left: 5px;
  width: 450px;
  color: rgb(255, 255, 255);
}
.detail-footer {
  font-size: 12px;
  color: #03b4f5;
  margin-top: 10px;
  margin-left: 5px;
  display: flex;
  justify-content: space-between;
}
.detail-title-right {
  width: 50px;
  text-align: center;
}
.left-top,
.right-top {
  font-size: 20px;
  color: white;
  text-align: center;
  padding-top: 10px;
  text-shadow: #b0b4c2 5px 5px 10px;
}
.left-button,
.right-button {
  font-size: 20px;
  color: white;
  text-align: center;
  padding-top: 5px;
  text-shadow: #b0b4c2 5px 5px 10px;
}
:deep(.el-select__wrapper) {
  background-color: #920a0a00; /* 自定义背景颜色: */
}

/* 更改鼠标悬停时的颜色 */
.el-select-dropdown .el-select-dropdown__item:hover {
  background-color: rgb(161, 159, 159); /* 自定义悬停颜色 */
}
.grid-content22 {
  /* background-color: #fff; */
  border-radius: 4px;
  height: 60px;
  background-image: url("../../assets/images/center_num.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ep-title {
  display: inline-block;
  font-size: 16px;
  font-weight: bold;
  color: white;
  width: 50px;
  height: 50px;
  text-align: center;
  padding: 1px 2px;
  background-color: #ffffff2b;
  border-radius: 5px;
}
.ep-sum {
  display: inline-block;
  font-size: 36px;
  font-weight: bold;
  color: white;
  width: 50px;
  height: 50px;
  text-align: center;
  padding: 1px 2px;
  background-color: #ffffff2b;
  border-radius: 5px;
}
.center-button {
  font-size: 20px;
  color: white;
  text-align: center;
  padding: 40px 0 20px 0;
  margin-right: 60%;
  text-shadow: #b0b4c2 5px 5px 10px;
}
</style>
