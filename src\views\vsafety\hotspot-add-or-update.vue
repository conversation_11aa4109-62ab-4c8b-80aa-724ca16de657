<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
          <el-form-item label="热点名" prop="name">
        <el-input v-model="dataForm.name" placeholder="热点名"></el-input>
      </el-form-item>
          <el-form-item label="来源" prop="source">
        <el-input v-model="dataForm.source" placeholder="来源"></el-input>
      </el-form-item>
          <el-form-item label="属性id" prop="attributeId">
        <el-input v-model="dataForm.attributeId" placeholder="属性id"></el-input>
      </el-form-item>
          <el-form-item label="信息链接" prop="url">
        <el-input v-model="dataForm.url" placeholder="信息链接"></el-input>
      </el-form-item>
          <el-form-item label="时间" prop="time">
        <el-input v-model="dataForm.time" placeholder="时间"></el-input>
      </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '',  name: '',  source: '',  attributeId: '',  url: '',  time: ''});

const rules = ref({
          name: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          source: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          attributeId: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          url: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          time: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ]
  });

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/vsafety/hotspot/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/vsafety/hotspot", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
