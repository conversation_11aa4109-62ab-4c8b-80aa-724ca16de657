<template>
  <div class="mod-vsafety__audio">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()" :icon="Plus">
          新增
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button 
          type="danger" 
          @click="state.deleteHandle()" 
          :icon="Delete"
          :disabled="!state.dataListSelections.length"
        >
          删除
        </el-button>
      </el-form-item>
    </el-form>
    
    <el-table 
      v-loading="state.dataListLoading" 
      :data="state.dataList" 
      border 
      @selection-change="state.dataListSelectionChangeHandle" 
      style="width: 100%"
      stripe
    >
      <el-table-column 
        type="selection" 
        header-align="center" 
        align="center" 
        width="50"
      ></el-table-column>
      <el-table-column prop="id" label="ID" header-align="center" align="center" width="80"></el-table-column>
      <el-table-column prop="url" label="音频链接" header-align="center" align="center">
        <template v-slot="scope">
          <el-button 
            type="primary" 
            link 
            @click="openAudioPlayer(scope.row.url)"
          >
            <el-icon><VideoPlay /></el-icon> 播放
          </el-button>
        </template>
      </el-table-column>
      <el-table-column 
        prop="platformId" 
        label="外键,关联平台表" 
        header-align="center" 
        align="center"
      ></el-table-column>
      <el-table-column 
        prop="detailId" 
        label="对应事件id" 
        header-align="center" 
        align="center"
      ></el-table-column>
      <el-table-column prop="time" label="时间" header-align="center" align="center" width="180"></el-table-column>
      <el-table-column 
        label="操作" 
        fixed="right" 
        header-align="center" 
        align="center" 
        width="200"
      >
        <template v-slot="scope">
          <el-button 
            type="primary" 
            link 
            @click="addOrUpdateHandle(scope.row.id)"
            :icon="Edit"
            class="action-btn"
          >
            修改
          </el-button>
          <el-button 
            type="danger" 
            link 
            @click="state.deleteHandle(scope.row.id)"
            :icon="Delete"
            class="action-btn"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <el-pagination 
      :current-page="state.page" 
      :page-sizes="[10, 20, 50, 100]" 
      :page-size="state.limit" 
      :total="state.total" 
      layout="total, sizes, prev, pager, next, jumper" 
      @size-change="state.pageSizeChangeHandle" 
      @current-change="state.pageCurrentChangeHandle"
      style="margin-top: 20px;"
    ></el-pagination>
    
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    
    <!-- 音频播放器对话框 -->
    <el-dialog 
      v-model="audioPlayerVisible" 
      title="音频播放" 
      width="30%"
      :close-on-click-modal="false"
      center
    >
      <div class="audio-player-container">
        <audio 
          ref="audioPlayer" 
          :src="currentAudioUrl" 
          controls 
          autoplay 
          style="width: 100%"
        ></audio>
      </div>
      <template #footer>
        <el-button @click="closeAudioPlayer">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./audio-add-or-update.vue";

// 新增音频播放相关状态
const audioPlayerVisible = ref(false);
const currentAudioUrl = ref("");
const audioPlayer = ref<HTMLAudioElement | null>(null);

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/vsafety/audio/page",
  getDataListIsPage: true,
  exportURL: "/vsafety/audio/export",
  deleteURL: "/vsafety/audio"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

// 打开音频播放器
const openAudioPlayer = (url: string) => {
  currentAudioUrl.value = url;
  audioPlayerVisible.value = true;
};

// 关闭音频播放器
const closeAudioPlayer = () => {
  audioPlayerVisible.value = false;
  // 暂停音频
  if (audioPlayer.value) {
    audioPlayer.value.pause();
  }
  currentAudioUrl.value = "";
};
</script>

<style lang="scss" scoped>
.mod-vsafety__audio {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  
  .el-form {
    margin-bottom: 20px;
    
    .el-form-item {
      margin-bottom: 0;
    }
  }
  
  .el-table {
    margin-top: 10px;
    
    :deep(.el-table__cell) {
      transition: all 0.3s ease;
      padding: 12px 0;
    }
    
    :deep(.el-table__row) {
      height: 60px;
      
      &:hover {
        .el-table__cell {
          background-color: #ecf5ff !important;
        }
      }
    }
    
    :deep(.el-table__header .el-table__cell) {
      background-color: #f8f8f9;
      font-weight: bold;
    }
  }
  
  .action-btn {
    padding: 8px 10px;
    transition: all 0.3s;
    margin: 0 5px;
    font-size: 14px;
    
    .el-icon {
      transition: transform 0.3s;
      margin-right: 5px;
    }
    
    span {
      transition: all 0.3s;
    }
  }
  
  .el-pagination {
    justify-content: flex-end;
    margin-top: 20px;
  }
  
  .audio-player-container {
    display: flex;
    justify-content: center;
    padding: 20px 0;
  }
}
</style>