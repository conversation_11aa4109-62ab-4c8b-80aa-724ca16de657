<template>
  <div class="mod-vsafety__envdetail">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()" :icon="Plus">
          新增
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button 
          type="danger" 
          @click="state.deleteHandle()" 
          :icon="Delete"
          :disabled="!state.dataListSelections.length"
        >
          删除
        </el-button>
      </el-form-item>
    </el-form>
    
    <el-table 
      v-loading="state.dataListLoading" 
      :data="state.dataList" 
      border 
      @selection-change="state.dataListSelectionChangeHandle" 
      style="width: 100%"
      stripe
    >
      <el-table-column 
        type="selection" 
        header-align="center" 
        align="center" 
        width="50"
      ></el-table-column>
      <el-table-column prop="id" label="ID" header-align="center" align="center" width="80"></el-table-column>
      <el-table-column prop="name" label="详情名" header-align="center" align="center" width="200"></el-table-column>
      <el-table-column prop="source" label="来源" header-align="center" align="center"></el-table-column>
      <el-table-column prop="author" label="作者" header-align="center" align="center"></el-table-column>
      <el-table-column prop="time" label="时间" header-align="center" align="center" width="180"></el-table-column>
      <el-table-column prop="evenId" label="事件ID" header-align="center" align="center"></el-table-column>
      <el-table-column prop="content" label="内容" header-align="center" align="center" width="200" show-overflow-tooltip></el-table-column>
      <el-table-column prop="natureId" label="性质ID" header-align="center" align="center"></el-table-column>
      <el-table-column prop="industryId" label="行业ID" header-align="center" align="center"></el-table-column>
      <el-table-column prop="url" label="URL" header-align="center" align="center" width="200">
        <template v-slot="scope">
          <!-- 修改：当URL为空时显示浅色文本 -->
          <template v-if="scope.row.url">
            <el-link type="primary" :href="scope.row.url" target="_blank" :underline="false">
              <el-icon><Link /></el-icon> 查看
            </el-link>
          </template>
          <template v-else>
            <span class="disabled-link">
              <el-icon><Link /></el-icon> 无链接
            </span>
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="provinceId" label="省份ID" header-align="center" align="center"></el-table-column>
      <el-table-column prop="cityId" label="城市ID" header-align="center" align="center"></el-table-column>
      <el-table-column prop="imgUrl" label="图片URL" header-align="center" align="center" width="200">
        <template v-slot="scope">
          <!-- 修改：当图片URL为空时显示浅色文本 -->
          <template v-if="scope.row.imgUrl">
            <el-link type="primary" :href="scope.row.imgUrl" target="_blank" :underline="false">
              <el-icon><Link /></el-icon> 查看
            </el-link>
          </template>
          <template v-else>
            <span class="disabled-link">
              <el-icon><Link /></el-icon> 无图片
            </span>
          </template>
        </template>
      </el-table-column>
      <el-table-column 
        label="操作" 
        fixed="right" 
        header-align="center" 
        align="center" 
        width="200"
      >
        <template v-slot="scope">
          <el-button 
            type="primary" 
            link 
            @click="addOrUpdateHandle(scope.row.id)"
            :icon="Edit"
            class="action-btn"
          >
            修改
          </el-button>
          <el-button 
            type="danger" 
            link 
            @click="state.deleteHandle(scope.row.id)"
            :icon="Delete"
            class="action-btn"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <el-pagination 
      :current-page="state.page" 
      :page-sizes="[10, 20, 50, 100]" 
      :page-size="state.limit" 
      :total="state.total" 
      layout="total, sizes, prev, pager, next, jumper" 
      @size-change="state.pageSizeChangeHandle" 
      @current-change="state.pageCurrentChangeHandle"
      style="margin-top: 20px;"
    ></el-pagination>
    
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./envdetail-add-or-update.vue";
import { Plus, Delete, Edit, Link } from '@element-plus/icons-vue';

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/vsafety/envdetail/page",
  getDataListIsPage: true,
  exportURL: "/vsafety/envdetail/export",
  deleteURL: "/vsafety/envdetail"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>

<style lang="scss" scoped>
.mod-vsafety__envdetail {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  
  .el-form {
    margin-bottom: 20px;
    
    .el-form-item {
      margin-bottom: 0;
    }
  }
  
  .el-table {
    margin-top: 10px;
    
    :deep(.el-table__cell) {
      transition: all 0.3s ease;
      padding: 12px 0;
    }
    
    :deep(.el-table__row) {
      height: 60px;
      
      &:hover {
        .el-table__cell {
          background-color: #ecf5ff !important;
        }
      }
    }
    
    :deep(.el-table__header .el-table__cell) {
      background-color: #f8f8f9;
      font-weight: bold;
    }
  }
  
  .action-btn {
    padding: 8px 10px;
    transition: all 0.3s;
    margin: 0 5px;
    font-size: 14px;
    
    .el-icon {
      transition: transform 0.3s;
      margin-right: 5px;
    }
    
    span {
      transition: all 0.3s;
    }
  }
  
  .el-pagination {
    justify-content: flex-end;
    margin-top: 20px;
  }
  
  .el-link {
    display: inline-flex;
    align-items: center;
    
    .el-icon {
      margin-right: 4px;
    }
  }
  
  /* 新增：当链接为空时的样式 */
  .disabled-link {
    display: inline-flex;
    align-items: center;
    color: #c0c4cc; /* 较浅的颜色 */
    cursor: not-allowed;
    
    .el-icon {
      margin-right: 4px;
      color: #c0c4cc; /* 图标也使用较浅的颜色 */
    }
  }
}
</style>