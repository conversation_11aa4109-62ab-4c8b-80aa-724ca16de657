<template>
  <div>
    <div v-if="url === '' || url?.split('//')[1] == ''">
      <h1> 😢 暂无内容，请从数据监测页面选择要查看的文章！</h1>
    </div>
    <iframe v-if="url !== '' && url?.split('//')[1] != ''" :src="url"></iframe>
  </div>
</template>

<script setup>
import { onActivated, watch, ref } from "vue";
let url = ref();
onActivated(() => {
  url.value = history.state.url || "";
  if (!url.value?.startsWith("http://") && !url.value?.startsWith("https://")) {
    url.value = "http://" + url.value;
    console.log("url.value", url.value);
    
  }
});
</script>

<style>
iframe {
  width: 100%;
  height: 90vh;
  border: none;
}
</style>