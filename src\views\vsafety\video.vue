<script setup>
import { onMounted, ref } from "vue";
import baseService from "@/service/baseService";
import PoDetailDialog from "/src/views/yuqing/po-detail-dialog.vue";
// 引入图标组件
import { Search, Refresh, Plus } from '@element-plus/icons-vue'

const loading = ref(false);
const dateList = ref([]);
const videoList = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const size = ref("default");
const background = ref(true);
const disabled = ref(false);
const total = ref(0);
const poDetailDialog = ref();

// 添加查询表单数据
const dataForm = ref({
  sentimentAnalysis: '',
  eventCategory: ''
});

const getPlatform = (platformId) => {
  const platform = {
    "-1": "未指定",
    1: "微博",
    2: "微信",
    3: "抖音",
  };
  return platform[platformId];
};

const getLabel = (labelId) => {
  const label = {
    0: "人身伤害",
    1: "交通事故",
    2: "自然灾害",
    3: "校园事件",
    4: "医疗卫生",
    5: "环境污染",
    6: "国家安全",
    7: "宗教文化",
    8: "其他负面",
    9: "非负样本",
    "-1": "未指定"
  };
  return label[labelId];
};

const getRisk = (riskId) => {
  const risk = {
    0: "低风险",
    1: "中风险",
    2: "高风险",
    "-1": "未指定"
  };
  return risk[riskId];
};

const getRiskColor = (riskId) => {
  const color = {
    0: "darkturquoise",
    1: "orange",
    2: "red"
  };
  return color[riskId];
};

// 添加重置查询方法
const resetQuery = () => {
  dataForm.value.sentimentAnalysis = '';
  dataForm.value.eventCategory = '';
  getData();
};

// 添加解析分析结果的方法
const getFirstEventInfo = (analysisResult) => {
  if (!analysisResult) return null;
  
  try {
    let parsed;
    if (typeof analysisResult === 'string') {
      parsed = JSON.parse(analysisResult);
    } else {
      parsed = analysisResult;
    }
    
    // 处理新的数据结构
    if (Array.isArray(parsed) && parsed.length > 0) {
      const firstItem = parsed[0];
      if (firstItem.events && Array.isArray(firstItem.events) && firstItem.events.length > 0) {
        return firstItem.events[0];
      }
    }
    
    // 保持原有逻辑以兼容旧数据结构
    if (Array.isArray(parsed) && parsed.length > 0 && parsed[0].events) {
      const events = parsed[0].events;
      if (Array.isArray(events) && events.length > 0) {
        return events[0];
      }
    }
    
    return null;
  } catch (error) {
    console.error('解析分析结果出错:', error);
    return null;
  }
};

function mergeVidUrlToDateList(dateArr, videoArr) {
  return dateArr.map(item => {
    const video = videoArr.find(v => v.id === item.videoId);
    return {
      ...item,
      vidUrl: video ? video.vidUrl : null
    };
  });
}

const getData = () => {
  loading.value = true;
  // 判断是否需要调用分类查询接口
  if (dataForm.value.sentimentAnalysis || dataForm.value.eventCategory) {
    // 调用分类查询接口
    getCategoryList();
  } else {
    // 调用原始分页接口
    getDefaultList();
  }
};

// 新增分类查询方法
const getCategoryList = () => {
  const params = {
    page: currentPage.value,
    limit: pageSize.value,
    ...dataForm.value
  };
  
  baseService.post("/vsafety/video/categoryPage", params).then((res) => {
    videoList.value = res.data.list;
    total.value = res.data.total;
    loading.value = false;
  }).catch((error) => {
    console.error("获取视频数据失败:", error);
    videoList.value = [];
    total.value = 0;
    loading.value = false;
  });
};

// 原始分页查询方法
const getDefaultList = () => {
  const params = {
    page: currentPage.value,
    limit: pageSize.value,
    ...dataForm.value
  };
  
  baseService.get("/vsafety/video/page", params).then((res) => {
    videoList.value = res.data.list;
    total.value = res.data.total;
    loading.value = false;
  }).catch((error) => {
    console.error("获取视频数据失败:", error);
    videoList.value = [];
    total.value = 0;
    loading.value = false;
  });
};

/* 当前页改变时的回调*/
const handleCurrentChange = (val) => {
  currentPage.value = val;
  getData();
};

/* 每页条数改变时的回调*/
const handleSizeChange = (val) => {
  currentPage.value = 1;
  pageSize.value = val;
  getData();
};

const handleDialog = (id, context, resource, type, grade, color) => {
  const detailParams = {
    id,
    context,
    resource,
    type,
    grade,
    color
  };
  poDetailDialog.value.init(detailParams);
};

onMounted(() => {
  getData();
});
</script>

<template>
  <div class="header">
    <el-header>
      <!-- 添加和picture.vue一样的查询选择 -->
      <el-form :inline="true" :model="dataForm" @keyup.enter="getData()">
        <!-- 新增的筛选查询组件 -->
        <el-form-item>
          <el-select v-model="dataForm.sentimentAnalysis" placeholder="请选择情感倾向性" clearable>
            <el-option label="正面" value="正面"></el-option>
            <el-option label="负面" value="负面"></el-option>
            <el-option label="中性" value="中性"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-select v-model="dataForm.eventCategory" placeholder="请选择事件分类" clearable>
            <el-option label="政治舆情" value="政治舆情"></el-option>
            <el-option label="经济舆情" value="经济舆情"></el-option>
            <el-option label="文化舆情" value="文化舆情"></el-option>
            <el-option label="社会舆情" value="社会舆情"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="getData()" :icon="Search">
            查询
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="resetQuery()" :icon="Refresh">
            重置
          </el-button>
        </el-form-item>
        <!-- 新增按钮 -->
        <el-form-item>
          <el-button type="primary" @click="addOrUpdateHandle()" :icon="Plus">
            新增
          </el-button>
        </el-form-item>
      </el-form>
    </el-header>
  </div>
  <div class="container">
    <el-container>
      <el-main v-loading="loading">
        <div v-show="videoList.length <= 0" style="text-align: center">暂无数据 !</div>
        <div class="video-grid">
          <div v-for="item in videoList" :key="item.id" class="video-box" 
               @click="handleDialog(item.id, item.context, getPlatform(item.platformId), getLabel(item.labelId), getRisk(item.riskId), getRiskColor(item.riskId))">
            <div class="video-thumb">
              <video
                v-if="item.vidUrl"
                :src="item.vidUrl"
                controls
                preload="auto"
                class="video-player"
                @click.stop="handleDialog(item.id, item.context, getPlatform(item.platformId), getLabel(item.labelId), getRisk(item.riskId), getRiskColor(item.riskId))"
              ></video>
              <div v-else class="no-video">无视频</div>
              <!-- 将舆论情感移到视频左上角 -->
              <div v-if="getFirstEventInfo(item.analysisResult)?.public_opinion_sentiment" 
                   class="sentiment-tag"
                   :class="getFirstEventInfo(item.analysisResult)?.public_opinion_sentiment === '正面' ? 'sentiment-positive' : 
                           getFirstEventInfo(item.analysisResult)?.public_opinion_sentiment === '负面' ? 'sentiment-negative' : 
                           'sentiment-neutral'">
                {{ getFirstEventInfo(item.analysisResult)?.public_opinion_sentiment }}
              </div>
            </div>
            <!-- 简化显示分析结果信息 -->
            <div v-if="item.analysisResult" class="analysis-info">
              <div class="info-item">
                <span class="label" >标题:</span>
                <span class="title-text">{{ getFirstEventInfo(item.analysisResult)?.event_title || '无' }}</span>
              </div>
              <div class="info-item">
                <span class="label">舆情分类:</span>
                <el-tag type="primary" size="small">
                  {{ getFirstEventInfo(item.analysisResult)?.public_opinion_category || '无' }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
    <!-- 弹窗 舆情详情 -->
    <po-detail-dialog ref="poDetailDialog"></po-detail-dialog>
  </div>
  <div class="demo-pagination-block">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :size="size"
      :disabled="disabled"
      :background="background"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped>
.el-header {
  background-color: #ebebeb80;
  padding: 10px 15px;
  height: 100%;
  line-height: 3;
}
.el-main {
  margin-top: 50px;
  background-color: #ebebeb80;
}
.demo-button-style {
  position: relative;
  top: -33px;
  left: 80px;
}
.demo-button-style {
  margin-top: 24px;
}
.header-content {
  display: flex;
  align-items: center;
}
.yuqing-label {
  margin-right: 10px;
  min-width: 70px;
}
.detail-item {
  /* margin-top: 20px; */
  padding: 10px;
  border-radius: 5px;
  /* background-color: #fff; */
}
.detail-title {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}
.detail-content {
  font-size: 14px;
  line-height: 2;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}
.detail-footer {
  font-size: 12px;
  color: #999;
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
}
.el-tag {
  margin-right: 20px;
  width: 80px;
}
.detail-title-left {
  cursor: pointer;
  color: #677fef;
}
.detail-title-left:hover {
  color: #4060ee;
}
.demo-pagination-block + .demo-pagination-block {
  margin-top: 10px;
}
.demo-pagination-block .demonstration {
  margin-bottom: 16px;
}
.common-header {
  text-align: center;
  margin-bottom: 30px;
}
.search-btn {
  background-color: #5b4cfe !important;
  color: #fff !important;
  border-radius: 0 5px 5px 0 !important;
}
.search-btn:hover {
  background-color: #7f71ff !important;
}
.video-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 24px;
  margin: 30px 0;
}
.video-box {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px #0000000a;
  padding: 16px 12px 12px 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: box-shadow 0.2s;
  cursor: pointer;
  min-height: 260px;
  position: relative;
}
.video-box:hover {
  box-shadow: 0 4px 16px #409eff33;
}
.video-thumb {
  width: 100%;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  position: relative;
}


.sentiment-tag {
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  color: white;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.sentiment-positive {
  background-color: #67c23a; /* 绿色 */
}

.sentiment-negative {
  background-color: #f56c6c; /* 红色 */
}

.sentiment-neutral {
  background-color: #e6a23c; /* 黄色 */
}

.video-player {
  width: 100%;
  max-height: 120px;
  object-fit: cover;
  border-radius: 6px;
  background: #000;
}
.no-video {
  width: 100%;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #bbb;
  background: #f5f5f5;
  border-radius: 6px;
}

.video-title {
  font-size: 15px;
  font-weight: bold;
  color: #4060ee;
  margin-bottom: 8px;
  cursor: pointer;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.video-title:hover {
  color: #1a3bb3;
}
.video-meta {
  margin-bottom: 8px;
}
.video-footer {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.analysis-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 8px 0;
  border-top: 1px solid #eee;
  margin-top: 8px;
}

.info-item {
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item .label {
  font-size: 13px;
  color: #666;
  white-space: nowrap;
}

.info-item .el-tag {
  font-size: 12px;
  padding: 0 8px;
  height: 22px;
  line-height: 22px;
}

@media (max-width: 1200px) {
  .video-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (max-width: 800px) {
  .video-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 500px) {
  .video-grid {
    grid-template-columns: 1fr;
  }
}
</style>