import http from '@/utils/http'

const baseUrl = '/api/v1'

// 安全API调用包装函数
export async function safeApiCall<T>(apiFunction: () => Promise<T>): Promise<T | {code: number, data: null, msg: string}> {
    try {
        return await apiFunction();
    } catch (error) {
        console.error('API调用错误: ', error);
        return { code: -1, data: null, msg: '请求失败' };
    }
}

// 文件上传
export function uploadFile(file: File, path?: string){
    const formData = new FormData();
    formData.append('file', file);
    if (path) {
        formData.append('path', path);
    }
    return http({
        url: baseUrl+'/files/upload',
        method: 'post',
        data: formData
    })
}

// 批量获取文件预签名URL
export function getBatchPresignedUrls(data: any){
    return http({
        url: baseUrl+'/files/batch/presigned-urls',
        method: 'post',
        data
    })
}

// 获取文件的预签名URL
export function getPresignedUrl(objectName: string){
    return http({
        url: baseUrl+'/files/presigned-url?objectName=' + objectName,
        method: 'get',
    })
}

// 下载文件
export function downloadFile(objectName: string){
    // window.open(baseUrl+'/files/download?objectName=' + objectName, '_blank')
    window.open(baseUrl+'/files/download?objectName=' + objectName, '_blank')
    // return http({
    //     url: baseUrl+'/files/download?objectName=' + objectName,
    //     method: 'get'
    // })
}

// 删除文件
export function deleteFile(objectName: string){
    return http({
        url: baseUrl+'/files/delete?objectName=' + objectName,
        method: 'delete'
    })
}   




