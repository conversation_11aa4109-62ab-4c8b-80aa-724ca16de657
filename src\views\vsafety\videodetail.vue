<template>
  <div class="mod-vsafety__videodetail">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()" :icon="Plus">
          新增
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button 
          type="danger" 
          @click="state.deleteHandle()" 
          :icon="Delete"
          :disabled="!state.dataListSelections.length"
        >
          删除
        </el-button>
      </el-form-item>
      <!-- 添加批量上传并分析按钮 -->
      <el-form-item>
        <el-upload
          :show-file-list="false"
          :http-request="handleBulkUpload"
          :before-upload="beforeUpload"
          accept="video/*"
          class="bulk-upload"
          :disabled="uploadLoading"
        >
          <el-button 
            type="success" 
            :icon="Upload"
            :loading="uploadLoading"
          >
            上传并分析
          </el-button>
        </el-upload>
      </el-form-item>
    </el-form>
    
    <el-table 
      v-loading="state.dataListLoading" 
      :data="state.dataList" 
      border 
      @selection-change="state.dataListSelectionChangeHandle" 
      style="width: 100%"
      stripe
    >
      <el-table-column 
        type="selection" 
        header-align="center" 
        align="center" 
        width="50"
      ></el-table-column>
      <el-table-column prop="id" label="ID" header-align="center" align="center" width="80"></el-table-column>
      <el-table-column prop="platformId" label="平台ID" header-align="center" align="center" width="100"></el-table-column>
      <el-table-column prop="labelId" label="标注ID" header-align="center" align="center" width="100"></el-table-column>
      <el-table-column prop="riskId" label="风险ID" header-align="center" align="center" width="100"></el-table-column>
      <el-table-column prop="context" label="主体内容" header-align="center" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="videoId" label="视频ID" header-align="center" align="center" width="100"></el-table-column>
      
      <el-table-column 
        label="操作" 
        fixed="right" 
        header-align="center" 
        align="center" 
        width="200"
      >
        <template v-slot="scope">
          <el-button 
            type="primary" 
            link 
            @click="addOrUpdateHandle(scope.row.id)"
            :icon="Edit"
            class="action-btn"
          >
            修改
          </el-button>
          <el-button 
            type="danger" 
            link 
            @click="state.deleteHandle(scope.row.id)"
            :icon="Delete"
            class="action-btn"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <el-pagination 
      :current-page="state.page" 
      :page-sizes="[10, 20, 50, 100]" 
      :page-size="state.limit" 
      :total="state.total" 
      layout="total, sizes, prev, pager, next, jumper" 
      @size-change="state.pageSizeChangeHandle" 
      @current-change="state.pageCurrentChangeHandle"
      style="margin-top: 20px;"
    ></el-pagination>
    
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"></add-or-update>
    
    <!-- 分析结果弹窗 -->
    <el-dialog
      v-model="analysisDialogVisible"
      title="视频分析结果"
      width="45%"
      :before-close="handleAnalysisDialogClose"
      class="analysis-result-dialog"
    >
      <div v-if="currentAnalysisResult" class="analysis-result-dialog">
        <!-- 视频播放 -->
        <div v-if="currentVideoUrl" class="video-preview-container">
          <video 
            controls 
            :src="currentVideoUrl"
            class="dialog-video-player"
          >
            您的浏览器不支持视频播放
          </video>
        </div>
        
        <!-- 事件数据展示 -->
        <div class="events-container">
          <el-divider content-position="left">事件信息</el-divider>
          <div v-if="parseEventsData(currentAnalysisResult).length > 0">
            <el-card 
              v-for="(event, index) in parseEventsData(currentAnalysisResult)" 
              :key="index" 
              class="event-card"
              shadow="hover"
            >
              <template #header>
                <div class="event-header">
                  <span class="event-title">{{ event.event_title }}</span>
                  <el-tag type="primary" size="small">ID: {{ event.event_id }}</el-tag>
                </div>
              </template>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="event-info-item">
                    <label>地点:</label>
                    <span>{{ event.location }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="event-info-item">
                    <label>时间:</label>
                    <span>{{ event.time }}</span>
                  </div>
                </el-col>
              </el-row>
              
              <div class="event-info-item">
                <label>参与者:</label>
                <span>
                  <el-tag 
                    v-for="(participant, pIndex) in event.participants" 
                    :key="pIndex" 
                    size="small" 
                    style="margin-right: 5px;"
                  >
                    {{ participant }}
                  </el-tag>
                </span>
              </div>
              
              <div class="event-info-item">
                <label>事件描述:</label>
                <span>{{ event.event_description }}</span>
              </div>
              
              <div class="event-info-item">
                <label>事件原因:</label>
                <span>{{ event.event_cause }}</span>
              </div>
              
              <div class="event-info-item">
                <label>事件结果:</label>
                <span>{{ event.event_outcome }}</span>
              </div>
              
              <!-- 场景时间线展示 -->
              <div v-if="event.scene_timeline && event.scene_timeline.length > 0" class="scene-timeline-container">
                <el-divider content-position="left">场景时间线</el-divider>
                <el-timeline>
                  <el-timeline-item
                    v-for="(item, sIndex) in event.scene_timeline"
                    :key="sIndex"
                    :timestamp="item.timestamp"
                    placement="top"
                  >
                    <el-card shadow="hover">
                      <p>{{ item.description }}</p>
                    </el-card>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </el-card>
          </div>
          <div v-else>
            <el-empty description="暂无事件数据" />
          </div>
          
          <!-- 时间线展示 -->
          <el-divider content-position="left">时间线</el-divider>
          <div v-if="parseTimelineData(currentAnalysisResult).length > 0" class="timeline-container">
            <el-timeline>
              <el-timeline-item
                v-for="(item, index) in parseTimelineData(currentAnalysisResult)"
                :key="index"
                :timestamp="item.timestamp"
                placement="top"
              >
                <el-card shadow="hover">
                  <p>{{ item.event }}</p>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </div>
          <div v-else>
            <el-empty description="暂无时间线数据" />
          </div>
          
          <!-- 标签展示 -->
          <el-divider content-position="left">关键词</el-divider>
          <div v-if="parseTagsData(currentAnalysisResult).length > 0" class="tags-container">
            <el-tag 
              v-for="(tag, index) in parseTagsData(currentAnalysisResult)" 
              :key="index" 
              type="primary" 
              size="large"
              style="margin-right: 10px; margin-bottom: 10px;"
            >
              {{ tag }}
            </el-tag>
          </div>
          <div v-else>
            <el-empty description="暂无关键词数据" />
          </div>
        </div>
      </div>
      <div v-else>
        <el-empty description="暂无分析结果" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="analysisDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./videodetail-add-or-update.vue";
import { Plus, Delete, Edit, Upload } from '@element-plus/icons-vue';
import baseService from "@/service/baseService";
import { ElMessage, ElLoading } from "element-plus";

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/vsafety/video/page",
  getDataListIsPage: true,
  exportURL: "/vsafety/video/export",
  deleteURL: "/vsafety/video"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 添加弹窗相关状态
const analysisDialogVisible = ref(false);
const currentAnalysisResult = ref<string | null>(null);
const currentVideoUrl = ref<string | null>(null);
const uploadLoading = ref(false); // 添加loading状态

// 上传前检查
const beforeUpload = (file: File) => {
  const isVideo = file.type.startsWith('video/');
  if (!isVideo) {
    ElMessage.error('请上传视频文件!');
  }
  return isVideo;
};

// 处理批量上传
const handleBulkUpload = async (params: any) => {
  const { file } = params;
  
  // 显示loading
  uploadLoading.value = true;
  let loadingInstance: any = null;
  try {
    loadingInstance = ElLoading.service({
      lock: true,
      text: '视频分析中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    const formData = new FormData();
    formData.append('file', file);
    
    const res = await baseService.post("/vsafety/video/upload", formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    if (res.code === 0) {
      ElMessage.success("视频分析完成");
      // 刷新列表以显示新数据
      state.getDataList();
      
      // 显示分析结果弹窗
      const responseData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
      const analysisData = responseData.analysisData || responseData;
      currentAnalysisResult.value = typeof analysisData === 'string' 
        ? analysisData 
        : JSON.stringify(analysisData);
      currentVideoUrl.value = responseData.fileUrl; // 设置当前视频 URL
      analysisDialogVisible.value = true;
    } else {
      ElMessage.error(res.msg || "视频分析失败");
    }
  } catch (err) {
    
  } finally {
    // 隐藏loading
    uploadLoading.value = false;
    if (loadingInstance) {
      loadingInstance.close();
    }
  }
};

// 通用数据解析函数
const parseData = (result: string, key: string): any[] => {
  try {
    // 如果result是字符串，先解析为JSON对象
    let parsed;
    if (typeof result === 'string') {
      parsed = JSON.parse(result);
    } else {
      parsed = result;
    }
    
    // 如果parsed是数组且第一个元素有指定的key
    if (Array.isArray(parsed) && parsed.length > 0 && parsed[0][key]) {
      return parsed[0][key];
    }
    
    // 如果parsed有指定的key且是数组，返回该key对应的值
    if (parsed[key] && Array.isArray(parsed[key])) {
      return parsed[key];
    }
    
    return [];
  } catch (error) {
    console.error(`解析${key}数据出错:`, error);
    return [];
  }
};

// 解析事件数据
const parseEventsData = (result: string): any[] => {
  return parseData(result, 'events');
};

// 解析时间线数据
const parseTimelineData = (result: string): any[] => {
  return parseData(result, 'timeline');
};

// 解析标签数据
const parseTagsData = (result: string): string[] => {
  return parseData(result, 'tags');
};

// 处理弹窗关闭
const handleAnalysisDialogClose = () => {
  analysisDialogVisible.value = false;
  currentAnalysisResult.value = null;
  currentVideoUrl.value = null;
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

</script>

<style lang="scss" scoped>
.mod-vsafety__videodetail {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  
  /* 分析结果弹窗样式 */
  .analysis-result-dialog {
    &.el-dialog {
      max-width: 800px;
      margin: 0 auto;
      
      .el-dialog__body {
        padding: 20px;
      }
    }
    
    .video-preview-container {
      margin-bottom: 20px;
      text-align: center;
      
      .dialog-video-player {
        width: 100%;
        max-width: 600px;
        outline: none;
        border-radius: 4px;
      }
    }
    
    .events-container {
      margin-top: 20px;
      
      .event-card {
        margin-bottom: 15px;
        
        .event-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .event-title {
            font-weight: bold;
            font-size: 16px;
          }
        }
        
        .event-info-item {
          display: flex;
          margin-bottom: 10px;
          line-height: 1.5;
          
          label {
            width: 100px;
            font-weight: bold;
            flex-shrink: 0;
          }
          
          span {
            flex: 1;
          }
        }
      }
      
      .scene-timeline-container,
      .timeline-container {
        margin-top: 20px;
        
        :deep(.el-timeline) {
          .el-timeline-item {
            .el-timeline-item__timestamp {
              font-size: 14px;
              color: #909399;
            }
            
            .el-card {
              cursor: default;
              
              p {
                margin: 0;
                font-size: 14px;
                line-height: 1.5;
              }
            }
          }
        }
      }
      
      .tags-container {
        padding: 10px 0;
      }
    }
    
    .el-empty {
      padding: 20px 0;
    }
  }
  
  .el-form {
    margin-bottom: 20px;
    
    .el-form-item {
      margin-bottom: 0;
    }
  }
  
  .el-table {
    margin-top: 10px;
    
    :deep(.el-table__cell) {
      transition: all 0.3s ease;
      padding: 12px 0;
    }
    
    :deep(.el-table__row) {
      height: 60px;
      
      &:hover {
        .el-table__cell {
          background-color: #ecf5ff !important;
        }
      }
    }
    
    :deep(.el-table__header .el-table__cell) {
      background-color: #f8f8f9;
      font-weight: bold;
    }
  }
  
  .action-btn {
    padding: 8px 10px;
    transition: all 0.3s;
    margin: 0 5px;
    font-size: 14px;
    
    .el-icon {
      transition: transform 0.3s;
      margin-right: 5px;
    }
    
    span {
      transition: all 0.3s;
    }
  }
  
  .el-pagination {
    justify-content: flex-end;
    margin-top: 20px;
  }
}
</style>