<script setup>
import { ref } from "vue";
import baseService from "@/service/baseService";
import "video.js/dist/video-js.css";
import { ElMessage } from "element-plus";

const visible = ref(false);
const video = ref([]);


let color = "";
let topic = "";
let source = "";
let type = "";
let grade = "";
let detailId = null;

// 添加视频分析相关状态
const videoAnalysisResults = ref({}); // 存储每个视频的分析结果
const videoAnalyzing = ref({}); // 存储每个视频的分析状态
const hasAnyAnalysis = ref(false); // 控制是否显示整个分析结果板块
// 添加视频信息存储
const videoInfoList = ref([]); // 存储视频详细信息

// 添加获取情感标签样式的方法
const getSentimentStyle = (sentiment) => {
  switch (sentiment) {
    case '正面':
      return { type: 'success', text: sentiment };
    case '负面':
      return { type: 'danger', text: sentiment };
    case '中性':
      return { type: 'warning', text: sentiment };
    default:
      return { type: 'info', text: sentiment || '无' };
  }
};



const getAudio = async (detailId) => {
  try {
    const response = await baseService.get("vsafety/audio/getAudioByDetailId", { detailId: detailId });
    audio.value = response.data.url;
  } catch (error) {
    console.error("获取音频信息失败:", error);
    audio.value = "";
  }
};

const getPictures = async (detailId) => {
  try {
    const response = await baseService.get("vsafety/picture/getPicsByDetailId", {
      detailId: detailId
    });
    picUrl.value = response.data.map((item) => item.picUrl);
  } catch (error) {
    console.error("获取图片信息失败:", error);
    picUrl.value = [];
  }
};

const getVideo = async (detailId) => {
  try {
    const response = await baseService.get("vsafety/video/page", {
      id: detailId
    });
    video.value = response.data.list.map((item) => item.vidUrl);
    // 保存视频详细信息
    videoInfoList.value = response.data.list;
    
    // 初始化视频分析结果和分析状态
    response.data.list.forEach((item, index) => {
      if (item.analysisResult) {
        videoAnalysisResults.value[index] = item.analysisResult;
      }
    });
    checkAnyAnalysis();
  } catch (error) {
    console.error("获取视频信息失败:", error);
    video.value = [];
    videoInfoList.value = [];
  }
};

const getTexts = async (detailId) => {
  try {
    const response = await baseService.get("vsafety/text/getTextsByDetailId", {
      detailId: detailId
    });
    texts.value = response.data.map((item) => item.text);
  } catch (error) {
    console.error("获取文本信息失败:", error);
    texts.value = [];
  }
};

// 添加视频分析函数
const analyzeVideo = async (videoUrl, index) => {
  try {
    // 设置正在分析状态
    videoAnalyzing.value[index] = true;
    
    // 获取对应视频的ID
    const videoId = videoInfoList.value[index]?.id;
    if (!videoId) {
      ElMessage.error("无法获取视频ID");
      return;
    }
    
    // 从URL获取视频文件
    const response = await fetch(videoUrl);
    const blob = await response.blob();
    
    // 从URL中提取干净的文件名，去除查询参数
    const urlWithoutParams = videoUrl.split('?')[0];
    let filename = urlWithoutParams.split('/').pop() || 'video.mp4';
    
    // 确保文件名有正确的扩展名
    if (!filename.includes('.')) {
      // 根据blob类型添加适当的扩展名
      if (blob.type.startsWith('video/mp4')) {
        filename += '.mp4';
      } else if (blob.type.startsWith('video/quicktime')) {
        filename += '.mov';
      } else if (blob.type.startsWith('video/webm')) {
        filename += '.webm';
      } else if (blob.type.startsWith('video/ogg')) {
        filename += '.ogv';
      } else {
        // 默认使用mp4扩展名
        filename += '.mp4';
      }
    }
    
    const file = new File([blob], filename, { type: blob.type });
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('id', videoId); // 添加视频ID参数
    
    const res = await baseService.post("/vsafety/video/analysis", formData);
    
    if (res.code === 0) {
      // 重新获取视频信息以获取分析结果
      const updatedVideoResponse = await baseService.get(`vsafety/video/${videoId}`);
      if (updatedVideoResponse.code === 0 && updatedVideoResponse.data.analysisResult) {
        // 保存分析结果
        videoAnalysisResults.value[index] = updatedVideoResponse.data.analysisResult;
        
        // 更新视频信息列表
        videoInfoList.value[index] = updatedVideoResponse.data;
        
        // 检查是否有任何分析结果，用于显示整个分析板块
        checkAnyAnalysis();
        
        ElMessage.success("视频分析完成");
      } else {
        ElMessage.error("视频分析完成，但未能获取分析结果");
      }
    } else {
      ElMessage.error(res.msg || "视频分析失败");
    }
  } catch (err) {
 
  } finally {
    videoAnalyzing.value[index] = false;
  }
};

// 检查是否有任何视频的分析结果
const checkAnyAnalysis = () => {
  hasAnyAnalysis.value = Object.keys(videoAnalysisResults.value).length > 0;
};

// 解析视频分析数据
const parseVideoAnalysisData = (result, key) => {
  try {
    let parsed;
    if (typeof result === 'string') {
      parsed = JSON.parse(result);
    } else {
      parsed = result;
    }
    
    // 处理新的数据结构
    if (Array.isArray(parsed) && parsed.length > 0) {
      const firstItem = parsed[0];
      if (key === 'events' && firstItem.events) {
        return firstItem.events;
      }
      if (key === 'timeline' && firstItem.timeline) {
        return firstItem.timeline;
      }
      if (key === 'tags' && firstItem.tags) {
        return firstItem.tags;
      }
    }
    
    // 保持原有逻辑以兼容旧数据结构
    if (Array.isArray(parsed) && parsed.length > 0 && parsed[0][key]) {
      return parsed[0][key];
    }
    
    if (parsed[key] && Array.isArray(parsed[key])) {
      return parsed[key];
    }
    
    return [];
  } catch (error) {
    console.error(`解析视频${key}数据出错:`, error);
    return [];
  }
};

const init = async (item) => {
  visible.value = true;
  console.log(item);
  topic = item.context;
  source = item.resource;
  type = item.type;
  grade = item.grade;
  color = item.color;
  detailId = item.id;
  
  try {
    await getVideo(item.id);
  } catch (error) {
    console.error("获取详情信息时发生错误:", error);
    ElMessage.error("数据加载失败，请稍后重试");
  }
  
  // 初始化时检查是否有分析结果
  checkAnyAnalysis();
};

const handleClose = () => {
  // 停止所有视频播放
  const videos = document.querySelectorAll("video");
  videos.forEach((video) => {
    video.pause();
    video.currentTime = 0; //重置播放位置
  });

  const audios = document.querySelectorAll("audio");
  audios.forEach((audio) => {
    audio.pause();
    audio.currentTime = 0;
  });

  visible.value = false;
};


defineExpose({
  init
});
</script>

<template>
  <el-dialog v-model="visible" :show-close="true" class="showAll_dialog" :close-on-click-modal="true" @close="handleClose" width="75vw" max-width="75vw" top="12.5vh" height="75vh">
    <div class="dialog-wrapper">
      <el-scrollbar>
        <!-- 主题 -->
        <!-- <div class="dialog-context">{{ topic }}</div> -->
        <div class="dialog-content">
          <!-- 视频内容 -->
          <div class="content-section">
            <div class="section-header">
              <h3 class="section-title">视频内容:</h3>
              <div class="info-container">
                <!-- 优化舆论情感和舆情分类显示 -->
                <span v-if="videoAnalysisResults[0]" class="info-sentiment">
                  舆论情感：
                  <el-tag 
                    :type="getSentimentStyle(parseVideoAnalysisData(videoAnalysisResults[0], 'events')[0]?.public_opinion_sentiment).type"
                    size="small"
                    class="sentiment-tag"
                  >
                    {{ getSentimentStyle(parseVideoAnalysisData(videoAnalysisResults[0], 'events')[0]?.public_opinion_sentiment).text }}
                  </el-tag>
                </span>
                <!-- 舆情分类显示为蓝色 -->
                <span v-if="videoAnalysisResults[0]" class="info-category">
                  舆情分类：<span style="color: #409eff;">{{ parseVideoAnalysisData(videoAnalysisResults[0], 'events')[0]?.public_opinion_category || '无' }}</span>
                </span>
              </div>
            </div>
            <div class="section-content">
              <div class="video-container">
                <div v-for="(vid, index) in video" :key="index" class="video-item">
                  <video :src="vid" controls preload="auto" class="video-player"></video>
                </div>
              
              </div>
            </div>
          </div>

          <!-- 视频分析结果板块 - 显示在整个视频板块下方 -->
          <div v-if="hasAnyAnalysis" class="content-section">
             <div class="section-header">
              <div style="display: flex; align-items: center; gap: 36px; position: relative;">
                <h3 class="section-title">视频分析结果:</h3>
                <!-- 视频分析按钮移到这里 -->
                <div class="video-analysis-buttons" v-if="video.length > 0" style="display: flex; gap: 8px; margin-top: 8px;">
                  <div v-for="(vid, index) in video" :key="index" class="video-analysis-button-item">
                    <el-button 
                      type="primary" 
                      @click="analyzeVideo(vid, index)"
                      :loading="videoAnalyzing[index]"
                      size="default"
                      :disabled="!vid"
                    >
                      <el-icon><Refresh /></el-icon>
                      <!-- 修改按钮文字逻辑 -->
                      {{ videoInfoList[index]?.analysisResult ? '重新分析' : '分析视频' }}
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
            <div class="section-content" >
              <div 
              
                v-for="(result, index) in videoAnalysisResults" 
                :key="index" 
                v-show="videoInfoList[index] && videoInfoList[index].analysisResult"
                class="video-analysis-results-container"
              >
                 <!-- 关键词 -->
                <div v-if="parseVideoAnalysisData(result, 'tags').length > 0" class="analysis-section">
                  <div class="analysis-section-header">关键词</div>
                  <div class="analysis-section-content">
                    <el-tag 
                      v-for="(tag, tagIndex) in parseVideoAnalysisData(result, 'tags')" 
                      :key="tagIndex"
                      type="primary"
                      size="default"
                      class="analysis-tag"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>
                </div>
                <!-- <el-divider content-position="left">视频 {{ parseInt(index) + 1 }} 分析结果</el-divider> -->
                <!-- 事件信息 -->
                <div v-if="parseVideoAnalysisData(result, 'events').length > 0" class="analysis-section">
                  <div class="analysis-section-header">事件信息</div>
                  <div class="analysis-section-content">
                    <div 
                      v-for="(event, eventIndex) in parseVideoAnalysisData(result, 'events')" 
                      :key="eventIndex"
                      class="analysis-item"
                    >
                      <el-card class="analysis-card">
                        <div class="analysis-card-content">
                          <p><strong>事件ID:</strong> {{ event.event_id }}</p>
                          <p><strong>标题:</strong> {{ event.event_title }}</p>
                          <p><strong>地点:</strong> {{ event.location }}</p>
                          <p><strong>时间:</strong> {{ event.time }}</p>
                          <p><strong>参与者:</strong> {{ event.participants?.join(', ') }}</p>
                          <p><strong>描述:</strong> {{ event.event_description }}</p>
                          <p><strong>原因:</strong> {{ event.event_cause }}</p>
                          <p><strong>结果:</strong> {{ event.event_outcome }}</p>
                          <p><strong>负面热点:</strong> {{ event.negative_hotspot_category || '无' }}</p>
                          
                          <div v-if="event.detailed_category && event.detailed_category.length > 0">
                            <p><strong>详细分类:</strong></p>
                            <el-tag 
                              v-for="(category, cIndex) in event.detailed_category" 
                              :key="cIndex"
                              type="success"
                              size="small"
                              style="margin-right: 5px; margin-bottom: 5px;"
                            >
                              {{ category }}
                            </el-tag>
                          </div>
                          
                          <div v-if="event.objects && event.objects.length > 0">
                            <p><strong>相关物品:</strong></p>
                            <el-tag 
                              v-for="(object, oIndex) in event.objects" 
                              :key="oIndex"
                              type="primary"
                              size="small"
                              style="margin-right: 5px; margin-bottom: 5px;"
                            >
                              {{ object }}
                            </el-tag>
                          </div>
                          
                          <!-- 场景时间线 -->
                          <div v-if="event.scene_timeline && event.scene_timeline.length > 0" class="scene-timeline">
                            <p><strong>场景时间线:</strong></p>
                            <div 
                              v-for="(scene, sceneIndex) in event.scene_timeline" 
                              :key="sceneIndex"
                              class="scene-item"
                            >
                              <span class="scene-frame">帧 {{ scene.frame }}:</span>
                              <span class="scene-time">{{ scene.timestamp }}</span>
                              <span class="scene-desc">{{ scene.description }}</span>
                            </div>
                          </div>
                        </div>
                      </el-card>
                    </div>
                  </div>
                </div>
                
                <!-- 时间线 -->
                <div v-if="parseVideoAnalysisData(result, 'timeline').length > 0" class="analysis-section">
                  <div class="analysis-section-header">时间线</div>
                  <div class="analysis-section-content">
                    <div 
                      v-for="(timelineItem, timelineIndex) in parseVideoAnalysisData(result, 'timeline')" 
                      :key="timelineIndex"
                      class="analysis-item"
                    >
                      <el-card class="analysis-card">
                        <div class="analysis-card-content">
                          <p><strong>时间:</strong> {{ timelineItem.timestamp }}</p>
                          <p><strong>事件:</strong> {{ timelineItem.event }}</p>
                        </div>
                      </el-card>
                    </div>
                  </div>
                </div>
                
                <!-- 关键词 -->
                <!-- <div v-if="parseVideoAnalysisData(result, 'tags').length > 0" class="analysis-section">
                  <div class="analysis-section-header">关键词</div>
                  <div class="analysis-section-content">
                    <el-tag 
                      v-for="(tag, tagIndex) in parseVideoAnalysisData(result, 'tags')" 
                      :key="tagIndex"
                      type="primary"
                      size="default"
                      class="analysis-tag"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>
                </div> -->
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </el-dialog>
</template>


<style scoped lang="less">
.dialog-wrapper {
  height: 75vh;
  display: flex;
  flex-direction: column;
}
:deep(.el-dialog) {
  margin: 12.5vh auto !important;
  width: 75vw !important;
  max-width: 75vw !important;
  height: 75vh;
  min-height: 75vh;
}

:deep(.el-dialog__body) {
  flex: 1;
  padding: 10px;
  overflow: hidden;
}
.dialog-content {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  gap: 32px;
}
.dialog-context {
  text-align: center;
  color: #3498db;
  font-family: "Arial", sans-serif;
  font-size: 24px;
  margin-bottom: 20px;
  font-weight: bold;
  letter-spacing: 1px;
}
.section-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 8px;
  position: relative;
}

.section-title {
  font-size: 18px;
  color: #333;
  font-weight: bold;
  border-left: 4px solid #409eff;
  padding-left: 10px;
  margin-bottom: 0;
}

.video-analysis-buttons {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex !important;
  gap: 8px;
  margin-top: 0 !important;
}

.video-analysis-button-item {
  margin-left: 0 !important;
}

.info-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.sentiment-tag {
  margin-left: -6px;
  font-weight: bold;
}

.info-grade {
  font-weight: bold;
}
.content-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px #0000000a;
  padding: 18px 16px 12px 16px;
  margin-bottom: 0;
}
.section-content {
  padding: 0 0;
}
.video-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 18px;
  width: 100%;
}
.video-item {
  background-color: #f8f8ff;
  border-radius: 6px;
  box-shadow: 0 1px 4px #0001;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  min-height: 260px;
}
.video-player {
  width: 100%;
  max-height: 333px;
  object-fit: contain;
  background: #000;
  border-radius: 4px;
}
.add-video-btn {
  border: 2px dashed #dcdfe6;
  background-color: #f5f7fa;
  cursor: pointer;
  transition: all 0.3s;
  min-height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  &:hover {
    border-color: #409eff;
    .add-icon {
      color: #409eff;
    }
  }
}
.add-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #909399;
  .el-icon {
    margin-bottom: 8px;
  }
}
.text-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.text-item {
  background: #f6faff;
  border-radius: 6px;
  padding: 10px 14px;
  margin-bottom: 0;
  font-size: 15px;
  color: #222;
  line-height: 1.7;
  box-shadow: 0 1px 2px #0000000a;
}
.audio-section {
  display: flex;
  justify-content: center;
  align-items: center;
}
.audio-player {
  width: 80%;
  min-width: 180px;
  max-width: 400px;
  margin: 10px 0;
}
.image-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 14px;
}
.image-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.preview-image {
  width: 100%;
  max-width: 180px;
  border-radius: 6px;
  box-shadow: 0 1px 4px #0001;
  transition: box-shadow 0.2s;
  &:hover {
    box-shadow: 0 4px 16px #409eff33;
  }
}

// 修改视频分析相关样式
.video-analysis-container {
  margin-top: 10px;
  display: flex;
  gap: 10px;
  justify-content: center;
  align-items: center;
  width: 100%;
  flex-wrap: wrap;
}

.video-analysis-results-container {
  margin-top: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #ebeef5;
  width: 100%;
  
  &:first-child {
    margin-top: 0;
  }
  
  :deep(.el-divider__text) {
    font-size: 16px;
    font-weight: bold;
    color: #409eff;
  }
  
  :deep(.el-divider) {
    margin-bottom: 25px;
  }
}

.analysis-section {
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.analysis-section-header {
  font-size: 15px;
  font-weight: bold;
  color: #606266;
  margin-bottom: 12px;
  padding: 8px 12px;
  background-color: #f2f6fc;
  border-radius: 4px;
  display: flex;
  align-items: center;
  
  &::before {
    content: "";
    display: inline-block;
    width: 3px;
    height: 14px;
    background-color: #409eff;
    margin-right: 8px;
  }
}

.analysis-section-content {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding: 0 5px;
}

.analysis-item {
  flex: 1;
  min-width: 250px;
}

.analysis-card {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  transition: all 0.3s;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
  
  .analysis-card-content {
    p {
      margin: 10px 0;
      font-size: 14px;
      line-height: 1.6;
      
      strong {
        color: #606266;
      }
    }
  }
}

.analysis-tag {
  margin-right: 10px;
  margin-bottom: 10px;
  background-color: #ecf5ff;
  border-color: #d9ecff;
  color: #409eff;
  
  &:hover {
    background-color: #409eff;
    color: white;
  }
}

// 添加场景时间线样式
.scene-timeline {
  margin-top: 10px;
  padding: 10px;
  background-color: #f0f2f5;
  border-radius: 4px;
  
  .scene-item {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 5px;
    padding: 5px 0;
    border-bottom: 1px solid #ebeef5;
    
    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
    
    .scene-frame {
      font-weight: bold;
      color: #409eff;
      flex: 0 0 auto;
    }
    
    .scene-time {
      background-color: #e1f3d8;
      color: #67c23a;
      padding: 2px 6px;
      border-radius: 4px;
      flex: 0 0 auto;
    }
    
    .scene-desc {
      flex: 1 1 auto;
    }
  }
}

@media (max-width: 600px) {
  .dialog-content {
    gap: 18px;
  }
  .content-section {
    padding: 10px 4px 8px 4px;
  }
  .section-title {
    font-size: 15px;
    padding-left: 6px;
  }
  .video-player {
    max-height: 140px;
  }
  .audio-player {
    width: 98%;
    max-width: 98vw;
  }
  .preview-image {
    max-width: 98vw;
  }
  .analysis-item {
    min-width: 100%;
  }
  
  .video-analysis-results-container {
    padding: 15px 10px;
  }
}
</style>
