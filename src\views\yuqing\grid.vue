<template>
  <div style="height: 100%">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="state.dataList"
      border
      style="width: 100%"
      :row-style="{ height: '80px' }"
    >
      <el-table-column prop="id" label="id" width="180" />
      <el-table-column prop="subject" label="事件名称"  :show-overflow-tooltip="true" width="180" />
      <el-table-column prop="currentDeptName" label="当前办理部门名称" width="250" />
      <el-table-column prop="issueDescription" label="问题描述" :show-overflow-tooltip="true" width="500" />
      <el-table-column prop="occurLocation" label="发生地点"  width="180"/>
      <el-table-column prop="occurTime" label="发生时间" />
      <el-table-column fixed="right" label="操作" min-width="120">
        <template #default="{row}">
          <el-button link type="primary" size="small" @click="state.deleteHandle(row.id);">
            删除
          </el-button>
          <el-button link type="primary" size="small" @click="handleEdit(row)">修改</el-button>
        </template>
      </el-table-column>
    </el-table>
    <grid-add-or-update ref="gridAddOrUpdateRef" @refreshDataList="state.getDataList"></grid-add-or-update>
    <el-pagination
      v-model:current-page="state.page"
      v-model:page-size="state.limit"
      :page-sizes="[10, 20, 50, 100]"
      :size="size"
      :disabled="disabled"
      :background="background"
      layout="total, sizes, prev, pager, next, jumper"
      :total="state.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import {ref,reactive,toRefs } from "vue";
import gridAddOrUpdate from "./grid-add-or-update.vue";
import useView from "@/hooks/useView";
const size = ref("default");
const background = ref(true);
const disabled = ref(false);
const gridAddOrUpdateRef = ref();
const view = reactive({
  getDataListURL: "/yuqing/grid/page",
  deleteURL: "/yuqing/grid",  
  getDataListIsPage: true,
  deleteIsBatch: true,     
  deleteIsBatchKey: "id", 
});
const state = reactive({ ...useView(view), ...toRefs(view) });

/* 当前页改变时的回调*/
const handleCurrentChange = (val) => {
  state.pageCurrentChangeHandle(val)
}

/* 每页条数改变时的回调*/
const handleSizeChange = (val) => {
  state.pageSizeChangeHandle(val)
}

const handleEdit = (row) =>{
  gridAddOrUpdateRef.value.init2(row.id);
}

const addOrUpdateHandle = () => {
  gridAddOrUpdateRef.value.init()
}
</script>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}

</style>
