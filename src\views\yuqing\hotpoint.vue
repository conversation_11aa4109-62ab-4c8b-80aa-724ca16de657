<template>
  <el-row :gutter="20">
    <el-col :span="8">
      <div class="grid-content ep-bg-purple">
        <span>政策</span>
        <!-- <el-divider /> -->
        <div v-for="(item, index) in policy?.slice(0, 4)" :key="index">
          <el-divider />
          <el-button @click="toUrl(item.url)" type="text">{{ item.name }}</el-button>
          <div class="source">来源：{{ item.source }}</div>
        </div>
      </div>
    </el-col>

    <el-col :span="8"
      ><div class="grid-content ep-bg-purple">
        <span>经济</span>
        <div v-for="(item, index) in economy?.slice(0, 4)" :key="index">
          <el-divider />
          <el-button @click="toUrl(item.url)" type="text">{{ item.name }}</el-button>
          <div class="source">来源：{{ item.source }}</div>
        </div>
      </div></el-col
    >
    <el-col :span="8"
      ><div class="grid-content ep-bg-purple">
        <span> 科技 </span>
        <div v-for="(item, index) in technology?.slice(0, 4)" :key="index">
          <el-divider />
          <el-button @click="toUrl(item.url)" type="text">{{ item.name }}</el-button>
          <div class="source">来源：{{ item.source }}</div>
        </div>
      </div></el-col
    >
  </el-row>
  <el-row :gutter="20">
    <el-col :span="8">
      <div class="grid-content ep-bg-purple">
        <span>头条热点</span>
        <!-- <el-divider /> -->
        <div v-for="(item, index) in headlines?.slice(0, 4)" :key="index">
          <el-divider />
          <el-button @click="toUrl(item.url)" type="text">{{ item.name }}</el-button>
          <div class="source">来源：{{ item.source }}</div>
        </div>
      </div>
    </el-col>

    <el-col :span="8"
      ><div class="grid-content ep-bg-purple">
        <span>微博热点</span>
        <div v-for="(item, index) in weibo?.slice(0, 4)" :key="index">
          <el-divider />
          <el-button @click="toUrl(item.url)" type="text">{{ item.name }}</el-button>
          <div class="source">来源：{{ item.source }}</div>
        </div>
      </div></el-col
    >
    <el-col :span="8"
      ><div class="grid-content ep-bg-purple">
        <span>百度热点 </span>
        <div v-for="(item, index) in baidu?.slice(0, 4)" :key="index">
          <el-divider />
          <el-button @click="toUrl(item.url)" type="text">{{ item.name }}</el-button>
          <div class="source">来源：{{ item.source }}</div>
        </div>
      </div></el-col
    >
  </el-row>
  <el-row :gutter="20">
    <el-col :span="24"
      ><div class="grid-content ep-bg-purple">
        <span>热点搜索词</span>
        <div class="bestsellers-container">
          <div id="charts-content"></div>
        </div></div
    ></el-col>
  </el-row>
  <el-row :gutter="20">
    <el-col :span="8">
      <div class="grid-content ep-bg-purple">
        <span>抖音热榜</span>
        <!-- <el-divider /> -->
        <div v-for="(item, index) in douyin?.slice(0, 4)" :key="index">
          <el-divider />
          <el-button @click="toUrl(item.url)" type="text">{{ item.name }}</el-button>
          <div class="source">来源：{{ item.source }}</div>
        </div>
      </div>
    </el-col>

    <el-col :span="8"
      ><div class="grid-content ep-bg-purple">
        <span>B站热点</span>
        <div v-for="(item, index) in bili?.slice(0, 4)" :key="index">
          <el-divider />
          <el-button @click="toUrl(item.url)" type="text">{{ item.name }}</el-button>
          <div class="source">来源：{{ item.source }}</div>
        </div>
      </div></el-col
    >
    <el-col :span="8"
      ><div class="grid-content ep-bg-purple">
        <span>腾讯热门</span>
        <div v-for="(item, index) in tencent?.slice(0, 4)" :key="index">
          <el-divider />
          <el-button @click="toUrl(item.url)" type="text">{{ item.name }}</el-button>
          <div class="source">来源：{{ item.source }}</div>
        </div>
      </div></el-col
    >
  </el-row>
  <el-tooltip class="box-item" effect="dark" content="Top Left prompts info" placement="bottom-start" :show-arrow="false"> </el-tooltip>
</template>

<script setup>
import baseService from "@/service/baseService";
import { onMounted, reactive } from "vue";
import * as echarts from "echarts";
import "echarts-wordcloud";
// 引入 lodash 中的 merge、深克隆
import merge from "lodash/merge";
import { ref } from "vue";
let seriesData = ref({
  gridSize: 30,
  data: []
});
//获取热词数据
baseService.get("yuqing/hotword/page").then((res) => {
  seriesData.value.data = res.data.list;
  DrawWordCloud();
});

// 词云图默认属性
const defaultSeries = [
  {
    type: "wordCloud",
    /**
     * circle（圆形）  词的数量不太多的时候，效果不明显，它会趋向于画一个椭圆
     * cardioid（苹果形或心形曲线）
     * diamond（菱形 正方形）
     * triangle-forward（三角形-向前）
     * triangle（三角形-直立）
     * pentagon（五边形）
     * star（星形）
     */
    shape: "circle",
    keepAspect: false,
    left: "center",
    top: "center",
    width: "100%",
    height: "100%",
    right: null,
    bottom: null,
    sizeRange: [12, 60],
    rotationRange: [-50, 50],
    rotationStep: 45,
    gridSize: 8,
    drawOutOfBound: false,
    layoutAnimation: true,
    // 这是全局的文字样式，相对应的还可以对每个词设置字体样式
    textStyle: {
      fontFamily: "sans-serif",
      fontWeight: "bold",
      // 颜色可以用一个函数来返回字符串
      color: function () {
        // 随机颜色
        return "rgb(" + [Math.round(Math.random() * 160), Math.round(Math.random() * 160), Math.round(Math.random() * 160)].join(",") + ")";
      }
    },
    // 鼠标hover的特效样式
    emphasis: {
      // focus: "none",
      focus: "self",
      textStyle: {
        textShadowBlur: 10,
        textShadowColor: "#999"
      }
    },
    data: []
  }
];

const series = merge(seriesData.value, defaultSeries[0], seriesData.value); // {}表示合并后的新对象，可以传入一个空对象作为初始值
// const series = merge({}, defaultSeries[0], seriesData.value); // {}表示合并后的新对象，可以传入一个空对象作为初始值
function DrawWordCloud() {
  // 词云
  let mychart = echarts.init(document.getElementById("charts-content")); // 可以设置主题色'dark'
  mychart.setOption({
    series: series,
    tooltip: {
      show: true,
      formatter: function (params) {
        return '"' + params.name + '"' + "<hr>" + "<div style='font-size:12px;margin-top:-8px;'>频率：" + params.data.value + "</div>";
      }
    }
  });
}

const policy = ref([]);
const economy = ref([]);
const technology = ref([]);
const headlines = ref([]);
const weibo = ref([]);
const baidu = ref([]);
const douyin = ref([]);
const bili = ref([]);
const tencent = ref([]);
const getId = (name) => {
  const compareTable = {
    政策: 1,
    经济: 2,
    科技: 3,
    头条热点: 4,
    微博热点: 5,
    百度热点: 6,
    抖音热榜: 7,
    B站热点: 8,
    腾讯热门: 9
  };
  return compareTable[name];
};
Promise.all([
  baseService.get("yuqing/hotspot/page", { attributeId: getId("政策") }),
  baseService.get("yuqing/hotspot/page", { attributeId: getId("经济") }),
  baseService.get("yuqing/hotspot/page", { attributeId: getId("科技") }),
  baseService.get("yuqing/hotspot/page", { attributeId: getId("头条热点") }),
  baseService.get("yuqing/hotspot/page", { attributeId: getId("微博热点") }),
  baseService.get("yuqing/hotspot/page", { attributeId: getId("百度热点") }),
  baseService.get("yuqing/hotspot/page", { attributeId: getId("抖音热榜") }),
  baseService.get("yuqing/hotspot/page", { attributeId: getId("B站热点") }),
  baseService.get("yuqing/hotspot/page", { attributeId: getId("腾讯热门") })
]).then(([respolicy, reseconomy, restechnology, resheadlines, resweibo, resbaidu, resdouyin, resbili, restencent]) => {
  policy.value = respolicy.data.list.sort((a, b) => new Date(b.time) - new Date(a.time));
  economy.value = reseconomy.data.list.sort((a, b) => new Date(b.time) - new Date(a.time));
  technology.value = restechnology.data.list.sort((a, b) => new Date(b.time) - new Date(a.time));
  headlines.value = resheadlines.data.list.sort((a, b) => new Date(b.time) - new Date(a.time));
  weibo.value = resweibo.data.list.sort((a, b) => new Date(b.time) - new Date(a.time));
  baidu.value = resbaidu.data.list.sort((a, b) => new Date(b.time) - new Date(a.time));
  douyin.value = resdouyin.data.list.sort((a, b) => new Date(b.time) - new Date(a.time));
  bili.value = resbili.data.list.sort((a, b) => new Date(b.time) - new Date(a.time));
  tencent.value = restencent.data.list.sort((a, b) => new Date(b.time) - new Date(a.time));
});
const toUrl = (url) => {
  //判断url包含http://或者https://
  if (!url.startsWith("http://") && !url.startsWith("https://")) {
    url = "http://" + url;
  }
  //跳转到新页面
  window.open(url);
};
</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 20px;
}
.el-row:last-child {
  margin-bottom: 0;
}
.el-col {
  border-radius: 5px;
}

.grid-content {
  border-radius: 5px;
  min-height: 300px;
  background-color: #ebebeb80;
  padding: 20px;
}
.el-divider {
  margin: 10px 0;
}
.el-button {
  display: block; /* 使用 block 属性，使整个按钮占据一行 */
  width: 100%; /* 固定宽度 */
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 用三个点表示超出文本 */
  text-align: left; /* 文本左对齐 */
  color: #000000af;
}
.el-button:hover {
  background-color: #ecebeb;
  color: #eb1f1fad;
}
.source {
  font-size: 12px;
  color: #999;
}
.bestsellers-container {
  height: 18.56rem;
  background: #f0f0f0;

  #charts-content {
    /* 需要设置宽高后才会显示 */
    background-color: #ffffff80;
    width: 100%;
    height: 100%;
  }
}
span {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}
</style>