<script setup>
import { onMounted, ref } from "vue";
import baseService from "@/service/baseService";
import PoDetailDialog from "/src/views/yuqing/po-detail-dialog.vue";
const loading = ref(false);
const dateList = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const size = ref("default");
const background = ref(true);
const disabled = ref(false);
const total = ref(0);
const poDetailDialog = ref();
const labelList = ref([]);
const platformList = ref([]);
const riskList = ref([]);
const checkboxGroup1 = ref("999");
const checkboxGroup2 = ref("999");
const checkboxGroup3 = ref("999");

const getPlatform = (platformId) => {
  const platform = {
    "-1": "未指定",
    0: "抖音",
    1: "微博",
    2: "微信"
  };
  return platform[platformId];
};

const getLabel = (labelId) => {
  const label = {
    0: "人身伤害",
    1: "交通事故",
    2: "自然灾害",
    3: "校园事件",
    4: "医疗卫生",
    5: "环境污染",
    6: "国家安全",
    7: "宗教文化",
    8: "其他负面",
    9: "非负样本",
    "-1": "未指定"
  };
  return label[labelId];
};

const getRisk = (riskId) => {
  const risk = {
    0: "低风险",
    1: "中风险",
    2: "高风险",
    "-1": "未指定"
  };
  return risk[riskId];
};

const getRiskColor = (riskId) => {
  const color = {
    0: "darkturquoise",
    1: "orange",
    2: "red"
  };
  return color[riskId];
};

const changeLabel = (value) => {
  currentPage.value = 1;
  console.log(value);
  getData();
};

const changeRisk = (value) => {
  currentPage.value = 1;
  getData();
};

const changePlatform = (value) => {
  currentPage.value = 1;
  getData();
};

const getData = () => {
  loading.value = true;
  const params = {
    page: currentPage.value,
    limit: pageSize.value,
    labelId: checkboxGroup1.value === "999" ? null : checkboxGroup1.value,
    riskId: checkboxGroup2.value === "999" ? null : checkboxGroup2.value,
    platformId: checkboxGroup3.value === "999" ? null : checkboxGroup3.value
  };
  baseService.get("/PublicOpinion/detail/page", params).then((res) => {
    dateList.value = res.data.list;
    total.value = res.data.total;
    loading.value = false;
  });
};
/* 当前页改变时的回调*/
const handleCurrentChange = (val) => {
  currentPage.value = val;
  getData();
};

/* 每页条数改变时的回调*/
const handleSizeChange = (val) => {
  currentPage.value = 1;
  pageSize.value = val;
  getData();
};

const handleDialog = (id, context, resource, type, grade, color) => {
  const detailParams = {
    id,
    context,
    resource,
    type,
    grade,
    color
  };
  poDetailDialog.value.init(detailParams);
};

onMounted(() => {
  Promise.all([baseService.get("PublicOpinion/detail/page"), baseService.get("label/page"), baseService.get("platform/page", { page: 1, size: 1000 }), baseService.get("risk/page", { page: 1, size: 1000 })]).then(([dateArray, labelArray, platformArray, riskArray]) => {
    dateList.value = dateArray.data.list;
    labelList.value = labelArray.data.list;
    platformList.value = platformArray.data.list;
    riskList.value = riskArray.data.list;
  });
});
</script>

<template>
  <div class="header">
    <el-header>
      <div class="header-content">
        <div class="yuqing-label">标注分类</div>
        <el-radio-group v-model="checkboxGroup1" size="small" @change="changeLabel">
          <el-radio-button value="999" style="margin-top: 5px; margin-right: 10px"> 全部 </el-radio-button>
          <el-radio-button v-for="item in labelList" :key="item.id" :value="item.id" style="margin-right: 10px">{{ item.label }}</el-radio-button>
        </el-radio-group>
      </div>
      <div class="header-content">
        <div class="yuqing-label">风险分类</div>
        <el-radio-group v-model="checkboxGroup2" size="small" @change="changeRisk">
          <el-radio-button value="999" style="margin-top: 5px; margin-right: 10px"> 全部 </el-radio-button>
          <el-radio-button v-for="item in riskList" :key="item.id" :value="item.id" style="margin-right: 10px">{{ item.risk }}</el-radio-button>
        </el-radio-group>
      </div>
      <div class="header-content">
        <div class="yuqing-label">平台分类</div>
        <el-radio-group v-model="checkboxGroup3" size="small" @change="changePlatform">
          <el-radio-button value="999" style="margin-top: 5px; margin-right: 10px"> 全部 </el-radio-button>
          <el-radio-button v-for="item in platformList" :key="item.id" :value="item.id" style="margin-right: 10px">{{ getPlatform(item.id) }}</el-radio-button>
        </el-radio-group>
      </div>
    </el-header>
  </div>
  <div class="container">
    <el-container>
      <el-main v-loading="loading">
        <div v-show="dateList.length <= 0" style="text-align: center">暂无数据 !</div>
        <div v-for="item in dateList" :key="item.id" class="detail-item">
          <div class="detail-title">
            <div class="detail-title-left" @click="handleDialog(item.id, item.context, getPlatform(item.platformId), getLabel(item.labelId), getRisk(item.riskId), getRiskColor(item.riskId))">主题({{ item.context }})</div>

            <span style="padding: 0 100px">
              <el-tag :color="getRiskColor(item.riskId)">{{ getRisk(item.riskId) }}</el-tag>
            </span>
          </div>
          <div class="detail-footer">来源：{{ getPlatform(item.platformId) }} 事件类型：{{ getLabel(item.labelId) }}</div>
          <el-divider />
        </div>
      </el-main>
    </el-container>
    <!-- 弹窗 舆情详情 -->
    <po-detail-dialog ref="poDetailDialog"></po-detail-dialog>
  </div>
  <div class="demo-pagination-block">
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      :size="size"
      :disabled="disabled"
      :background="background"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<style scoped>
.el-header {
  background-color: #ebebeb80;
  padding: 10px 15px;
  height: 100%;
  line-height: 3;
}
.el-main {
  margin-top: 50px;
  background-color: #ebebeb80;
}
.demo-button-style {
  position: relative;
  top: -33px;
  left: 80px;
}
.demo-button-style {
  margin-top: 24px;
}
.header-content {
  display: flex;
  align-items: center;
}
.yuqing-label {
  margin-right: 10px;
  min-width: 70px;
}
.detail-item {
  /* margin-top: 20px; */
  padding: 10px;
  border-radius: 5px;
  /* background-color: #fff; */
}
.detail-title {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}
.detail-content {
  font-size: 14px;
  line-height: 2;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}
.detail-footer {
  font-size: 12px;
  color: #999;
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
}
.el-tag {
  margin-right: 20px;
  width: 80px;
}
.detail-title-left {
  cursor: pointer;
  color: #677fef;
}
.detail-title-left:hover {
  color: #4060ee;
}
.demo-pagination-block + .demo-pagination-block {
  margin-top: 10px;
}
.demo-pagination-block .demonstration {
  margin-bottom: 16px;
}
.common-header {
  text-align: center;
  margin-bottom: 30px;
}
.search-btn {
  background-color: #5b4cfe !important;
  color: #fff !important;
  border-radius: 0 5px 5px 0 !important;
}
.search-btn:hover {
  background-color: #7f71ff !important;
}
</style>
